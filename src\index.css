@import url('https://fonts.googleapis.com/css2?family=Noto+Serif+JP:wght@300;400;500;700;900&family=Noto+Sans+JP:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Samurai Color Palette */
  --samurai-charcoal: #2C2C2C;
  --samurai-red: #CC2936;
  --samurai-gold: #D4AF37;
  --samurai-white: #F5F5F5;
  --samurai-gray: #6B6B6B;
  --samurai-navy: #1B1B2F;
  --samurai-black: #1A1A1A;
  
  font-family: 'Noto Sans JP', -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
  line-height: 1.6;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Traditional Japanese Typography */
.font-serif-jp {
  font-family: 'Noto Serif JP', serif;
}

.font-sans-jp {
  font-family: 'Noto Sans JP', sans-serif;
}

/* Traditional Japanese Animations */
@keyframes brushStroke {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Animated Japanese Characters Background */
@keyframes traverseRight {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  10% {
    opacity: 0.2;
  }
  90% {
    opacity: 0.2;
  }
  100% {
    transform: translateX(calc(100vw + 100px));
    opacity: 0;
  }
}

@keyframes traverseLeft {
  0% {
    transform: translateX(calc(100vw + 100px));
    opacity: 0;
  }
  10% {
    opacity: 0.2;
  }
  90% {
    opacity: 0.2;
  }
  100% {
    transform: translateX(-100px);
    opacity: 0;
  }
}

@keyframes traverseDown {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  10% {
    opacity: 0.15;
  }
  90% {
    opacity: 0.15;
  }
  100% {
    transform: translateY(calc(100vh + 100px));
    opacity: 0;
  }
}

.animate-brush-stroke {
  animation: brushStroke 0.8s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-gentle-float {
  animation: gentleFloat 4s ease-in-out infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Traditional Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--samurai-white);
  border: 1px solid var(--samurai-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--samurai-charcoal);
  border-radius: 0;
  border: 1px solid var(--samurai-gray);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--samurai-red);
}

/* Traditional Japanese Paper Effect */
.paper-texture {
  background: linear-gradient(45deg, var(--samurai-white) 0%, #f8f8f8 100%);
  position: relative;
}

.paper-texture::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(120, 119, 108, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(120, 119, 108, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(120, 119, 108, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

/* Samurai Card Style */
.samurai-card {
  background: var(--samurai-white);
  border: 2px solid var(--samurai-charcoal);
  box-shadow: 4px 4px 0px var(--samurai-charcoal);
  transition: all 0.3s ease;
}

.samurai-card:hover {
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px var(--samurai-charcoal);
}

/* Traditional Japanese Brush Stroke Effect */
.brush-stroke {
  position: relative;
  display: inline-block;
}

.brush-stroke::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--samurai-red) 0%, var(--samurai-gold) 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.6s ease;
}

.brush-stroke:hover::after {
  transform: scaleX(1);
}

/* Torii Gate Silhouette */
.torii-gate {
  position: relative;
  width: 200px;
  height: 120px;
  margin: 0 auto;
}

.torii-gate::before,
.torii-gate::after {
  content: '';
  position: absolute;
  background: var(--samurai-charcoal);
}

.torii-gate::before {
  width: 100%;
  height: 8px;
  top: 20px;
  border-radius: 4px;
}

.torii-gate::after {
  width: 100%;
  height: 6px;
  top: 40px;
  border-radius: 3px;
}

/* Traditional Button Style */
.samurai-button {
  background: var(--samurai-charcoal);
  color: var(--samurai-white);
  border: 2px solid var(--samurai-charcoal);
  padding: 12px 24px;
  font-family: 'Noto Sans JP', sans-serif;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.samurai-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--samurai-gold), transparent);
  transition: left 0.5s ease;
}

.samurai-button:hover::before {
  left: 100%;
}

.samurai-button:hover {
  background: var(--samurai-red);
  border-color: var(--samurai-red);
  transform: translateY(-2px);
}

/* Animated Japanese Characters Background */
.japanese-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.japanese-char {
  position: absolute;
  font-family: 'Noto Serif JP', serif;
  font-weight: 300;
  color: var(--samurai-charcoal);
  opacity: 0.15;
  user-select: none;
  pointer-events: none;
}

.japanese-char.traverse-right {
  animation: traverseRight linear infinite;
}

.japanese-char.traverse-left {
  animation: traverseLeft linear infinite;
}

.japanese-char.traverse-down {
  animation: traverseDown linear infinite;
}

.japanese-char.size-sm {
  font-size: 1.5rem;
}

.japanese-char.size-md {
  font-size: 2.5rem;
}

.japanese-char.size-lg {
  font-size: 4rem;
}

.japanese-char.size-xl {
  font-size: 6rem;
}