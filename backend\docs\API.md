# 道場コーチ (<PERSON><PERSON><PERSON> Coach) API Documentation

## Overview

The Dōjō Coach API is a RESTful API built with Go and Gin framework, providing comprehensive endpoints for Japanese language learning with gamification features.

**Base URL**: `http://localhost:8080/api/v1`

## Authentication

Most endpoints require authentication via Clerk JWT tokens. Include the token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

## Response Format

### Success Response
```json
{
  "data": { ... },
  "success": true,
  "message": "Optional success message"
}
```

### Error Response
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": { ... }
  },
  "success": false
}
```

## Rate Limiting

The API implements intelligent rate limiting:
- **General endpoints**: 100 requests per minute
- **Authentication endpoints**: 10 requests per minute  
- **AI Sensei conversation**: 20 requests per minute

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## Endpoints

### Health Check

#### GET /health
Check API health status.

**Response:**
```json
{
  "status": "healthy",
  "service": "Dōjō Coach Backend API",
  "version": "1.0.0",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Authentication

#### POST /auth/webhook
Clerk webhook handler for user lifecycle events.

**Headers:**
- `svix-id`: Webhook ID
- `svix-timestamp`: Webhook timestamp
- `svix-signature`: Webhook signature

### User Management

#### GET /user/profile
Get current user profile.

**Authentication:** Required

**Response:**
```json
{
  "data": {
    "id": "uuid",
    "clerk_id": "user_xxx",
    "email": "<EMAIL>",
    "display_name": "User Name",
    "japanese_level": "beginner",
    "current_rank": "白帯",
    "experience_points": 150,
    "study_streak": 5,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "success": true
}
```

#### PUT /user/profile
Update user profile.

**Authentication:** Required

**Request Body:**
```json
{
  "display_name": "New Name",
  "japanese_level": "intermediate",
  "learning_goals": ["vocabulary", "grammar"]
}
```

### Lessons

#### GET /lessons
Get lessons with optional filters.

**Authentication:** Required

**Query Parameters:**
- `level` (string): Filter by difficulty level
- `category` (string): Filter by lesson category
- `sensei` (string): Filter by assigned sensei
- `limit` (int): Number of results (default: 20)
- `offset` (int): Pagination offset (default: 0)

**Response:**
```json
{
  "data": {
    "lessons": [
      {
        "id": "uuid",
        "title": "Basic Greetings",
        "description": "Learn essential Japanese greetings",
        "level": "beginner",
        "category": "conversation",
        "sensei": "Tanaka-sensei",
        "content": { ... },
        "is_completed": false
      }
    ],
    "total": 50,
    "limit": 20,
    "offset": 0
  },
  "success": true
}
```

#### POST /lessons/:id/complete
Mark lesson as completed.

**Authentication:** Required

**Request Body:**
```json
{
  "score": 85,
  "time_spent": 1200
}
```

### Vocabulary

#### GET /vocabulary
Get vocabulary words with filters.

**Authentication:** Required

**Query Parameters:**
- `jlpt_level` (string): Filter by JLPT level (N5, N4, N3, N2, N1)
- `category` (string): Filter by category
- `review_only` (boolean): Only words needing review
- `limit` (int): Number of results (default: 20)
- `offset` (int): Pagination offset (default: 0)

**Response:**
```json
{
  "data": {
    "vocabulary": [
      {
        "id": "uuid",
        "word": "こんにちは",
        "reading": "こんにちは",
        "romaji": "konnichiwa",
        "meaning": "hello, good afternoon",
        "jlpt_level": "N5",
        "category": "greetings",
        "progress": {
          "mastery_level": 75,
          "next_review_at": "2024-01-16T10:00:00Z"
        }
      }
    ],
    "total": 100,
    "limit": 20,
    "offset": 0
  },
  "success": true
}
```

#### GET /vocabulary/stats
Get vocabulary learning statistics.

**Authentication:** Required

**Response:**
```json
{
  "data": {
    "overall": {
      "total": 500,
      "mastered": 120,
      "learning": 80,
      "new": 300,
      "due_review": 15
    },
    "by_jlpt": [
      {
        "jlpt_level": "N5",
        "total": 200,
        "mastered": 80
      }
    ]
  },
  "success": true
}
```

### Grammar

#### GET /grammar
Get grammar points with filters.

**Authentication:** Required

**Query Parameters:**
- `level` (string): Filter by difficulty level
- `category` (string): Filter by grammar category
- `needs_review` (boolean): Only grammar needing review
- `limit` (int): Number of results (default: 20)
- `offset` (int): Pagination offset (default: 0)

#### GET /grammar/:id/exercises
Get practice exercises for a grammar point.

**Authentication:** Required

**Response:**
```json
{
  "data": {
    "grammar_point": { ... },
    "exercises": [
      {
        "type": "fill_blank",
        "question": "彼は毎日学校___行きます。",
        "options": ["に", "で", "を", "が"],
        "correct": "に",
        "explanation": "「行く」の動詞には「に」を使います。"
      }
    ]
  },
  "success": true
}
```

### Progress Tracking

#### POST /progress/vocabulary
Update vocabulary learning progress.

**Authentication:** Required

**Request Body:**
```json
{
  "vocabulary_id": "uuid",
  "is_correct": true,
  "response_time": 3.5,
  "difficulty_rating": 3
}
```

### AI Sensei

#### POST /sensei/conversation
Start or continue a conversation session.

**Authentication:** Required

**Request Body:**
```json
{
  "message": "こんにちは、元気ですか？",
  "session_id": "uuid" // optional for continuing session
}
```

**Response:**
```json
{
  "data": {
    "session_id": "uuid",
    "sensei_response": "こんにちは！元気です、ありがとうございます。",
    "feedback": {
      "grammar_score": 95,
      "suggestions": []
    }
  },
  "success": true
}
```

#### POST /sensei/grammar-check
Check grammar of Japanese text.

**Authentication:** Required

**Request Body:**
```json
{
  "text": "私は学校に行きました。"
}
```

### Achievements

#### GET /achievements
Get all achievements with user progress.

**Authentication:** Required

**Response:**
```json
{
  "data": {
    "achievements": [
      {
        "id": "uuid",
        "title": "First Steps",
        "description": "Complete your first lesson",
        "category": "learning",
        "experience_reward": 10,
        "is_unlocked": true,
        "unlocked_at": "2024-01-15T10:00:00Z"
      }
    ]
  },
  "success": true
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `UNAUTHORIZED` | Authentication required |
| `FORBIDDEN` | Access denied |
| `NOT_FOUND` | Resource not found |
| `CONFLICT` | Resource conflict |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `DATABASE_ERROR` | Database operation failed |
| `INTERNAL_SERVER_ERROR` | Unexpected server error |

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `429` - Too Many Requests
- `500` - Internal Server Error
- `503` - Service Unavailable
