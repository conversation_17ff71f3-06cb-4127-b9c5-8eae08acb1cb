package main

import (
	"log"
	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"

	"japancoach-backend/internal/database"
	"japancoach-backend/internal/handlers"
	"japancoach-backend/internal/middleware"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Initialize database
	db, err := database.Initialize()
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer database.CloseDB(db)

	// Initialize Gin router
	r := gin.New()

	// Add middleware
	r.Use(middleware.LoggingMiddleware())
	r.Use(middleware.ErrorHandler())
	r.Use(middleware.APIRateLimitMiddleware())
	r.Use(gin.Recovery())

	// CORS middleware
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:5173", "http://localhost:3000"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))

	// Initialize handlers
	userHandler := handlers.NewUserHandler(db)
	lessonHandler := handlers.NewLessonHandler(db)
	progressHandler := handlers.NewProgressHandler(db)
	senseiHandler := handlers.NewSenseiHandler(db)
	vocabularyHandler := handlers.NewVocabularyHandler(db)
	grammarHandler := handlers.NewGrammarHandler(db)

	// Public routes
	public := r.Group("/api/v1")
	{
		public.GET("/health", handlers.HealthCheck)
		public.POST("/auth/webhook", userHandler.HandleClerkWebhook)
	}

	// Protected routes
	protected := r.Group("/api/v1")
	protected.Use(middleware.ClerkAuth())
	{
		// User routes
		protected.GET("/user/profile", userHandler.GetProfile)
		protected.PUT("/user/profile", userHandler.UpdateProfile)
		protected.GET("/user/progress", userHandler.GetUserProgress)

		// Lesson routes
		protected.GET("/lessons", lessonHandler.GetLessons)
		protected.GET("/lessons/:id", lessonHandler.GetLesson)
		protected.POST("/lessons/:id/complete", lessonHandler.CompleteLesson)

		// Progress routes
		protected.GET("/progress/overview", progressHandler.GetOverview)
		protected.POST("/progress/vocabulary", progressHandler.UpdateVocabularyProgress)
		protected.POST("/progress/grammar", progressHandler.UpdateGrammarProgress)

		// AI Sensei routes
		protected.POST("/sensei/conversation", senseiHandler.StartConversation)
		protected.POST("/sensei/grammar-check", senseiHandler.CheckGrammar)
		protected.GET("/sensei/recommendations", senseiHandler.GetRecommendations)

		// Achievement routes
		protected.GET("/achievements", progressHandler.GetAchievements)
		protected.POST("/achievements/:id/unlock", progressHandler.UnlockAchievement)

		// Vocabulary routes
		protected.GET("/vocabulary", vocabularyHandler.GetVocabulary)
		protected.GET("/vocabulary/:id", vocabularyHandler.GetVocabularyWord)
		protected.GET("/vocabulary/categories", vocabularyHandler.GetVocabularyCategories)
		protected.GET("/vocabulary/jlpt-levels", vocabularyHandler.GetJLPTLevels)
		protected.GET("/vocabulary/search", vocabularyHandler.SearchVocabulary)
		protected.GET("/vocabulary/stats", vocabularyHandler.GetVocabularyStats)

		// Grammar routes
		protected.GET("/grammar", grammarHandler.GetGrammarPoints)
		protected.GET("/grammar/:id", grammarHandler.GetGrammarPoint)
		protected.GET("/grammar/:id/exercises", grammarHandler.GetGrammarExercises)
		protected.GET("/grammar/categories", grammarHandler.GetGrammarCategories)
		protected.GET("/grammar/search", grammarHandler.SearchGrammar)
		protected.GET("/grammar/stats", grammarHandler.GetGrammarStats)
	}

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Server starting on port %s", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
