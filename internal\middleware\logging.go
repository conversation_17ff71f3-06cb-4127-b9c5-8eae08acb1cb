package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

// LoggingMiddleware logs HTTP requests and responses
func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		var statusColor, methodColor, resetColor string
		if param.IsOutputColor() {
			statusColor = param.StatusCodeColor()
			methodColor = param.MethodColor()
			resetColor = param.ResetColor()
		}

		if param.Latency > time.Minute {
			param.Latency = param.Latency.Truncate(time.Second)
		}

		return fmt.Sprintf("[DOJO-COACH] %v |%s %3d %s| %13v | %15s |%s %-7s %s %#v\n%s",
			param.TimeStamp.Format("2006/01/02 - 15:04:05"),
			statusColor, param.StatusCode, resetColor,
			param.Latency,
			param.ClientIP,
			methodColor, param.Method, resetColor,
			param.Path,
			param.ErrorMessage,
		)
	})
}

// RequestResponseLogger logs detailed request and response information
func RequestResponseLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Log request
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// Create a response writer wrapper to capture response
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw

		start := time.Now()
		c.Next()
		latency := time.Since(start)

		// Log the request and response details
		logEntry := map[string]interface{}{
			"timestamp":     start.Format(time.RFC3339),
			"method":        c.Request.Method,
			"path":          c.Request.URL.Path,
			"query":         c.Request.URL.RawQuery,
			"status_code":   c.Writer.Status(),
			"latency_ms":    latency.Milliseconds(),
			"client_ip":     c.ClientIP(),
			"user_agent":    c.Request.UserAgent(),
			"request_size":  len(requestBody),
			"response_size": blw.body.Len(),
		}

		// Add user ID if available
		if userID, exists := GetUserIDFromContext(c); exists {
			logEntry["user_id"] = userID
		}

		// Add request body for non-GET requests (but mask sensitive data)
		if c.Request.Method != "GET" && len(requestBody) > 0 {
			var requestJSON map[string]interface{}
			if err := json.Unmarshal(requestBody, &requestJSON); err == nil {
				// Mask sensitive fields
				maskSensitiveFields(requestJSON)
				logEntry["request_body"] = requestJSON
			}
		}

		// Add response body for errors
		if c.Writer.Status() >= 400 {
			var responseJSON map[string]interface{}
			if err := json.Unmarshal(blw.body.Bytes(), &responseJSON); err == nil {
				logEntry["response_body"] = responseJSON
			}
		}

		// Log as JSON
		logJSON, _ := json.Marshal(logEntry)
		log.Printf("REQUEST_LOG: %s", string(logJSON))
	}
}

// bodyLogWriter wraps gin.ResponseWriter to capture response body
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// maskSensitiveFields masks sensitive information in request logs
func maskSensitiveFields(data map[string]interface{}) {
	sensitiveFields := []string{
		"password", "token", "secret", "key", "authorization",
		"clerk_secret", "database_url", "jwt", "session",
	}

	for _, field := range sensitiveFields {
		if _, exists := data[field]; exists {
			data[field] = "***MASKED***"
		}
	}

	// Recursively mask nested objects
	for _, value := range data {
		if nestedMap, ok := value.(map[string]interface{}); ok {
			maskSensitiveFields(nestedMap)
		}
	}
}

// ErrorLogger logs application errors with context
func LogError(c *gin.Context, err error, message string, additionalData ...map[string]interface{}) {
	logEntry := map[string]interface{}{
		"timestamp":  time.Now().Format(time.RFC3339),
		"level":      "ERROR",
		"message":    message,
		"error":      err.Error(),
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"client_ip":  c.ClientIP(),
		"user_agent": c.Request.UserAgent(),
	}

	// Add user ID if available
	if userID, exists := GetUserIDFromContext(c); exists {
		logEntry["user_id"] = userID
	}

	// Add additional data if provided
	if len(additionalData) > 0 {
		for key, value := range additionalData[0] {
			logEntry[key] = value
		}
	}

	logJSON, _ := json.Marshal(logEntry)
	log.Printf("ERROR_LOG: %s", string(logJSON))
}

// LogInfo logs informational messages
func LogInfo(message string, data map[string]interface{}) {
	logEntry := map[string]interface{}{
		"timestamp": time.Now().Format(time.RFC3339),
		"level":     "INFO",
		"message":   message,
	}

	for key, value := range data {
		logEntry[key] = value
	}

	logJSON, _ := json.Marshal(logEntry)
	log.Printf("INFO_LOG: %s", string(logJSON))
}

// LogWarning logs warning messages
func LogWarning(message string, data map[string]interface{}) {
	logEntry := map[string]interface{}{
		"timestamp": time.Now().Format(time.RFC3339),
		"level":     "WARNING",
		"message":   message,
	}

	for key, value := range data {
		logEntry[key] = value
	}

	logJSON, _ := json.Marshal(logEntry)
	log.Printf("WARNING_LOG: %s", string(logJSON))
}
