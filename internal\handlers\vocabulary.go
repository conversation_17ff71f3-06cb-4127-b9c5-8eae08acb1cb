package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"japancoach-backend/internal/middleware"
	"japancoach-backend/internal/models"
)

type VocabularyHandler struct {
	db *gorm.DB
}

func NewVocabularyHandler(db *gorm.DB) *VocabularyHandler {
	return &VocabularyHandler{db: db}
}

// GetVocabulary returns vocabulary words based on user's level and filters
func (h *VocabularyHandler) GetVocabulary(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user to determine their level
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Parse query parameters
	jlptLevel := c.Query("jlpt_level")
	category := c.Query("category")
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")
	reviewOnly := c.Query("review_only") == "true"

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)

	// Build query
	query := h.db.Model(&models.Vocabulary{})

	if jlptLevel != "" {
		query = query.Where("jlpt_level = ?", jlptLevel)
	}

	if category != "" {
		query = query.Where("category = ?", category)
	}

	// If review_only is true, get words that need review
	if reviewOnly {
		subQuery := h.db.Model(&models.VocabularyProgress{}).
			Select("vocabulary_id").
			Where("user_id = ? AND next_review_at <= NOW()", user.ID)
		
		query = query.Where("id IN (?)", subQuery)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get vocabulary with user progress
	var vocabulary []models.Vocabulary
	if err := query.
		Preload("Progress", "user_id = ?", user.ID).
		Order("frequency_rank ASC").
		Limit(limit).
		Offset(offset).
		Find(&vocabulary).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch vocabulary"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"vocabulary": vocabulary,
		"total":      total,
		"limit":      limit,
		"offset":     offset,
	})
}

// GetVocabularyWord returns a specific vocabulary word by ID
func (h *VocabularyHandler) GetVocabularyWord(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	wordID := c.Param("id")
	wordUUID, err := uuid.Parse(wordID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vocabulary ID"})
		return
	}

	var vocabulary models.Vocabulary
	if err := h.db.
		Preload("Progress", "user_id = ?", user.ID).
		Where("id = ?", wordUUID).
		First(&vocabulary).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Vocabulary word not found"})
		return
	}

	c.JSON(http.StatusOK, vocabulary)
}

// GetVocabularyCategories returns available vocabulary categories
func (h *VocabularyHandler) GetVocabularyCategories(c *gin.Context) {
	var categories []string
	if err := h.db.Model(&models.Vocabulary{}).
		Distinct("category").
		Where("category IS NOT NULL AND category != ''").
		Pluck("category", &categories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"categories": categories,
	})
}

// GetJLPTLevels returns available JLPT levels
func (h *VocabularyHandler) GetJLPTLevels(c *gin.Context) {
	var levels []string
	if err := h.db.Model(&models.Vocabulary{}).
		Distinct("jlpt_level").
		Where("jlpt_level IS NOT NULL AND jlpt_level != ''").
		Order("jlpt_level").
		Pluck("jlpt_level", &levels).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch JLPT levels"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"levels": levels,
	})
}

// SearchVocabulary searches vocabulary by word, reading, or meaning
func (h *VocabularyHandler) SearchVocabulary(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	searchTerm := c.Query("q")
	if searchTerm == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search term is required"})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, _ := strconv.Atoi(limitStr)

	var vocabulary []models.Vocabulary
	if err := h.db.
		Preload("Progress", "user_id = ?", user.ID).
		Where("word ILIKE ? OR reading ILIKE ? OR romaji ILIKE ? OR meaning ILIKE ?",
			"%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%").
		Order("frequency_rank ASC").
		Limit(limit).
		Find(&vocabulary).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search vocabulary"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"vocabulary": vocabulary,
		"count":      len(vocabulary),
		"search_term": searchTerm,
	})
}

// GetVocabularyStats returns vocabulary learning statistics
func (h *VocabularyHandler) GetVocabularyStats(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Get vocabulary statistics
	var stats struct {
		Total     int64 `json:"total"`
		Mastered  int64 `json:"mastered"`
		Learning  int64 `json:"learning"`
		New       int64 `json:"new"`
		DueReview int64 `json:"due_review"`
	}

	// Total vocabulary in progress
	h.db.Model(&models.VocabularyProgress{}).
		Where("user_id = ?", user.ID).
		Count(&stats.Total)

	// Mastered (>= 80% mastery)
	h.db.Model(&models.VocabularyProgress{}).
		Where("user_id = ? AND mastery_level >= ?", user.ID, 80).
		Count(&stats.Mastered)

	// Learning (0-79% mastery)
	h.db.Model(&models.VocabularyProgress{}).
		Where("user_id = ? AND mastery_level > ? AND mastery_level < ?", user.ID, 0, 80).
		Count(&stats.Learning)

	// Due for review
	h.db.Model(&models.VocabularyProgress{}).
		Where("user_id = ? AND next_review_at <= NOW()", user.ID).
		Count(&stats.DueReview)

	// New words (not yet started)
	var totalAvailable int64
	h.db.Model(&models.Vocabulary{}).Count(&totalAvailable)
	stats.New = totalAvailable - stats.Total

	// Get stats by JLPT level
	var jlptStats []struct {
		JLPTLevel string `json:"jlpt_level"`
		Total     int64  `json:"total"`
		Mastered  int64  `json:"mastered"`
	}

	h.db.Raw(`
		SELECT 
			v.jlpt_level,
			COUNT(*) as total,
			COUNT(CASE WHEN vp.mastery_level >= 80 THEN 1 END) as mastered
		FROM vocabulary v
		LEFT JOIN vocabulary_progress vp ON v.id = vp.vocabulary_id AND vp.user_id = ?
		WHERE v.jlpt_level IS NOT NULL
		GROUP BY v.jlpt_level
		ORDER BY v.jlpt_level
	`, user.ID).Scan(&jlptStats)

	c.JSON(http.StatusOK, gin.H{
		"overall":    stats,
		"by_jlpt":    jlptStats,
	})
}
