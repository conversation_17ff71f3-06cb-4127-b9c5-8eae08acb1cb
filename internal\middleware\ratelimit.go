package middleware

import (
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter represents a simple in-memory rate limiter
type RateLimiter struct {
	visitors map[string]*Visitor
	mutex    sync.RWMutex
	rate     int           // requests per window
	window   time.Duration // time window
}

// Visitor represents a client's rate limit state
type Visitor struct {
	requests  int
	lastReset time.Time
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(rate int, window time.Duration) *RateLimiter {
	rl := &RateLimiter{
		visitors: make(map[string]*Visitor),
		rate:     rate,
		window:   window,
	}

	// Start cleanup goroutine
	go rl.cleanup()

	return rl
}

// Allow checks if a request should be allowed
func (rl *RateLimiter) Allow(clientID string) bool {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()
	visitor, exists := rl.visitors[clientID]

	if !exists {
		rl.visitors[clientID] = &Visitor{
			requests:  1,
			lastReset: now,
		}
		return true
	}

	// Reset if window has passed
	if now.Sub(visitor.lastReset) > rl.window {
		visitor.requests = 1
		visitor.lastReset = now
		return true
	}

	// Check if under limit
	if visitor.requests < rl.rate {
		visitor.requests++
		return true
	}

	return false
}

// cleanup removes old visitors periodically
func (rl *RateLimiter) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		rl.mutex.Lock()
		now := time.Now()
		for clientID, visitor := range rl.visitors {
			if now.Sub(visitor.lastReset) > rl.window*2 {
				delete(rl.visitors, clientID)
			}
		}
		rl.mutex.Unlock()
	}
}

// RateLimitMiddleware creates a rate limiting middleware
func RateLimitMiddleware(rate int, window time.Duration) gin.HandlerFunc {
	limiter := NewRateLimiter(rate, window)

	return func(c *gin.Context) {
		clientID := c.ClientIP()

		// Use user ID if authenticated for more accurate limiting
		if userID, exists := GetUserIDFromContext(c); exists {
			clientID = userID
		}

		if !limiter.Allow(clientID) {
			LogWarning("Rate limit exceeded", map[string]interface{}{
				"client_id": clientID,
				"path":      c.Request.URL.Path,
				"method":    c.Request.Method,
			})

			RateLimitError(c)
			c.Abort()
			return
		}

		c.Next()
	}
}

// APIRateLimitMiddleware applies different rate limits based on endpoint type
func APIRateLimitMiddleware() gin.HandlerFunc {
	// Different rate limiters for different endpoint types
	generalLimiter := NewRateLimiter(100, time.Minute)     // 100 requests per minute
	authLimiter := NewRateLimiter(10, time.Minute)         // 10 auth requests per minute
	conversationLimiter := NewRateLimiter(20, time.Minute) // 20 conversation requests per minute

	return func(c *gin.Context) {
		clientID := c.ClientIP()
		path := c.Request.URL.Path

		// Use user ID if authenticated
		if userID, exists := GetUserIDFromContext(c); exists {
			clientID = userID
		}

		var limiter *RateLimiter
		var limitType string

		// Choose appropriate limiter based on path
		switch {
		case contains(path, "/auth/"):
			limiter = authLimiter
			limitType = "auth"
		case contains(path, "/sensei/conversation"):
			limiter = conversationLimiter
			limitType = "conversation"
		default:
			limiter = generalLimiter
			limitType = "general"
		}

		if !limiter.Allow(clientID) {
			LogWarning("Rate limit exceeded", map[string]interface{}{
				"client_id":  clientID,
				"path":       path,
				"method":     c.Request.Method,
				"limit_type": limitType,
			})

			RateLimitError(c)
			c.Abort()
			return
		}

		c.Next()
	}
}

// contains checks if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr ||
		len(s) > len(substr) && s[len(s)-len(substr):] == substr ||
		(len(s) > len(substr) && findSubstring(s, substr))
}

// findSubstring finds if substr exists in s
func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
