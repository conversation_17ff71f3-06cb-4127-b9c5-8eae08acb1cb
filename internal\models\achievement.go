package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Achievement represents a gamification achievement
type Achievement struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"not null"`
	NameJP      string    `json:"name_jp"` // Japanese name
	Description string    `json:"description" gorm:"not null"`
	Icon        string    `json:"icon"` // Icon identifier or URL
	
	// Achievement criteria
	Type        string `json:"type" gorm:"not null"` // lesson_completion, vocabulary_mastery, streak, etc.
	Criteria    string `json:"criteria" gorm:"type:text"` // JSON object with criteria details
	Points      int    `json:"points" gorm:"default:0"` // Experience points awarded
	
	// Rarity and display
	Rarity      string `json:"rarity" gorm:"default:'common'"` // common, rare, epic, legendary
	BadgeColor  string `json:"badge_color" gorm:"default:'#gray'"`
	IsHidden    bool   `json:"is_hidden" gorm:"default:false"` // Hidden until unlocked
	
	// Ordering and categorization
	Category    string `json:"category"` // learning, social, milestone, special
	Order       int    `json:"order" gorm:"default:0"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	UserAchievements []UserAchievement `json:"user_achievements,omitempty"`
}

// UserAchievement represents a user's unlocked achievement
type UserAchievement struct {
	ID            uuid.UUID   `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID        uuid.UUID   `json:"user_id" gorm:"type:uuid;not null"`
	AchievementID uuid.UUID   `json:"achievement_id" gorm:"type:uuid;not null"`
	User          User        `json:"user" gorm:"foreignKey:UserID"`
	Achievement   Achievement `json:"achievement" gorm:"foreignKey:AchievementID"`

	// Achievement details
	UnlockedAt    time.Time `json:"unlocked_at"`
	Progress      int       `json:"progress" gorm:"default:0"` // Progress towards achievement (0-100)
	IsCompleted   bool      `json:"is_completed" gorm:"default:false"`
	
	// Additional context
	UnlockContext string    `json:"unlock_context"` // JSON with context about how it was unlocked
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeCreate sets the ID before creating
func (a *Achievement) BeforeCreate(tx *gorm.DB) error {
	if a.ID == uuid.Nil {
		a.ID = uuid.New()
	}
	return nil
}

func (ua *UserAchievement) BeforeCreate(tx *gorm.DB) error {
	if ua.ID == uuid.Nil {
		ua.ID = uuid.New()
	}
	return nil
}
