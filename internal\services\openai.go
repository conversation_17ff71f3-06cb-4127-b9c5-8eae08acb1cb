package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"
)

// OpenAIService handles interactions with OpenAI API
type OpenAIService struct {
	APIKey  string
	BaseURL string
	Client  *http.Client
}

// NewOpenAIService creates a new OpenAI service instance
func NewOpenAIService() *OpenAIService {
	return &OpenAIService{
		APIKey:  os.Getenv("OPENAI_API_KEY"),
		BaseURL: "https://api.openai.com/v1",
		Client: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// OpenAI API request/response structures
type OpenAIRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	Temperature float64   `json:"temperature,omitempty"`
	MaxTokens   int       `json:"max_tokens,omitempty"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type OpenAIResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// RoadmapGenerationRequest represents the input for roadmap generation
type RoadmapGenerationRequest struct {
	CurrentLevel     string   `json:"current_level"`
	TargetLevel      string   `json:"target_level"`
	LearningGoals    []string `json:"learning_goals"`
	FocusAreas       []string `json:"focus_areas"`
	SpecialInterests []string `json:"special_interests"`
	StudyTimePerDay  int      `json:"study_time_per_day"`
	PreferredPace    string   `json:"preferred_pace"`
}

// RoadmapGenerationResponse represents the AI-generated roadmap structure
type RoadmapGenerationResponse struct {
	Title             string      `json:"title"`
	Description       string      `json:"description"`
	EstimatedDuration int         `json:"estimated_duration"`
	Milestones        []Milestone `json:"milestones"`
}

type Milestone struct {
	Title              string       `json:"title"`
	Description        string       `json:"description"`
	Order              int          `json:"order"`
	EstimatedDays      int          `json:"estimated_days"`
	LearningObjectives []string     `json:"learning_objectives"`
	SkillsToAcquire    []string     `json:"skills_to_acquire"`
	LessonPlans        []LessonPlan `json:"lesson_plans"`
}

type LessonPlan struct {
	Title          string   `json:"title"`
	Description    string   `json:"description"`
	Order          int      `json:"order"`
	LessonType     string   `json:"lesson_type"`
	Difficulty     string   `json:"difficulty"`
	EstimatedTime  int      `json:"estimated_time"`
	ContentOutline []string `json:"content_outline"`
}

// LessonGenerationRequest represents the input for lesson content generation
type LessonGenerationRequest struct {
	Title          string   `json:"title"`
	LessonType     string   `json:"lesson_type"`
	Difficulty     string   `json:"difficulty"`
	ContentOutline []string `json:"content_outline"`
	UserLevel      string   `json:"user_level"`
	FocusAreas     []string `json:"focus_areas"`
}

// LessonGenerationResponse represents the AI-generated lesson content
type LessonGenerationResponse struct {
	Content       LessonContent    `json:"content"`
	Exercises     []Exercise       `json:"exercises"`
	Vocabulary    []VocabularyItem `json:"vocabulary"`
	GrammarPoints []GrammarPoint   `json:"grammar_points"`
	CulturalNotes []CulturalNote   `json:"cultural_notes"`
}

type LessonContent struct {
	Introduction string         `json:"introduction"`
	MainContent  []ContentBlock `json:"main_content"`
	Summary      string         `json:"summary"`
}

type ContentBlock struct {
	Type     string `json:"type"` // text, dialogue, example, explanation
	Title    string `json:"title,omitempty"`
	Content  string `json:"content"`
	Japanese string `json:"japanese,omitempty"`
	Romaji   string `json:"romaji,omitempty"`
}

type Exercise struct {
	Type          string   `json:"type"` // multiple_choice, fill_blank, translation, matching
	Question      string   `json:"question"`
	Options       []string `json:"options,omitempty"`
	CorrectAnswer string   `json:"correct_answer"`
	Explanation   string   `json:"explanation"`
}

type VocabularyItem struct {
	Word               string `json:"word"`
	Reading            string `json:"reading"`
	Romaji             string `json:"romaji"`
	Meaning            string `json:"meaning"`
	PartOfSpeech       string `json:"part_of_speech"`
	Example            string `json:"example"`
	ExampleTranslation string `json:"example_translation"`
}

type GrammarPoint struct {
	Pattern     string   `json:"pattern"`
	Explanation string   `json:"explanation"`
	Usage       string   `json:"usage"`
	Examples    []string `json:"examples"`
}

type CulturalNote struct {
	Topic       string `json:"topic"`
	Explanation string `json:"explanation"`
	Context     string `json:"context"`
}

// GenerateRoadmap creates a personalized learning roadmap using AI
func (s *OpenAIService) GenerateRoadmap(req RoadmapGenerationRequest) (*RoadmapGenerationResponse, error) {
	if s.APIKey == "" {
		return nil, fmt.Errorf("OpenAI API key not configured")
	}

	prompt := s.buildRoadmapPrompt(req)

	openAIReq := OpenAIRequest{
		Model: "gpt-4",
		Messages: []Message{
			{
				Role:    "system",
				Content: "You are an expert Japanese language teacher creating personalized learning roadmaps. Respond only with valid JSON.",
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Temperature: 0.7,
		MaxTokens:   4000,
	}

	response, err := s.makeRequest(openAIReq)
	if err != nil {
		return nil, fmt.Errorf("failed to generate roadmap: %w", err)
	}

	var roadmap RoadmapGenerationResponse
	if err := json.Unmarshal([]byte(response.Choices[0].Message.Content), &roadmap); err != nil {
		return nil, fmt.Errorf("failed to parse roadmap response: %w", err)
	}

	return &roadmap, nil
}

// GenerateLesson creates lesson content using AI
func (s *OpenAIService) GenerateLesson(req LessonGenerationRequest) (*LessonGenerationResponse, error) {
	if s.APIKey == "" {
		return nil, fmt.Errorf("OpenAI API key not configured")
	}

	prompt := s.buildLessonPrompt(req)

	openAIReq := OpenAIRequest{
		Model: "gpt-4",
		Messages: []Message{
			{
				Role:    "system",
				Content: "You are an expert Japanese language teacher creating detailed lesson content. Respond only with valid JSON.",
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Temperature: 0.7,
		MaxTokens:   4000,
	}

	response, err := s.makeRequest(openAIReq)
	if err != nil {
		return nil, fmt.Errorf("failed to generate lesson: %w", err)
	}

	var lesson LessonGenerationResponse
	if err := json.Unmarshal([]byte(response.Choices[0].Message.Content), &lesson); err != nil {
		return nil, fmt.Errorf("failed to parse lesson response: %w", err)
	}

	return &lesson, nil
}

// makeRequest sends a request to OpenAI API
func (s *OpenAIService) makeRequest(req OpenAIRequest) (*OpenAIResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", s.BaseURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+s.APIKey)

	resp, err := s.Client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("OpenAI API error: %s", string(body))
	}

	var response OpenAIResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &response, nil
}

// buildRoadmapPrompt creates a detailed prompt for roadmap generation
func (s *OpenAIService) buildRoadmapPrompt(req RoadmapGenerationRequest) string {
	return fmt.Sprintf(`Create a personalized Japanese learning roadmap with the following requirements:

Current Level: %s
Target Level: %s
Learning Goals: %v
Focus Areas: %v
Special Interests: %v
Study Time Per Day: %d minutes
Preferred Pace: %s

Please create a comprehensive roadmap with:
1. A clear title and description
2. Estimated total duration in days
3. 4-6 major milestones, each with:
   - Clear title and description
   - Estimated duration in days
   - 3-5 specific learning objectives
   - 2-4 key skills to acquire
   - 5-8 lesson plans with:
     - Descriptive title
     - Brief description
     - Lesson type (vocabulary, grammar, conversation, writing, reading, listening, culture)
     - Difficulty level
     - Estimated time in minutes
     - Content outline (3-5 key points to cover)

Focus on practical, engaging content that builds progressively. Include cultural context and real-world applications.

Respond with valid JSON matching this structure:
{
  "title": "string",
  "description": "string",
  "estimated_duration": number,
  "milestones": [
    {
      "title": "string",
      "description": "string",
      "order": number,
      "estimated_days": number,
      "learning_objectives": ["string"],
      "skills_to_acquire": ["string"],
      "lesson_plans": [
        {
          "title": "string",
          "description": "string",
          "order": number,
          "lesson_type": "string",
          "difficulty": "string",
          "estimated_time": number,
          "content_outline": ["string"]
        }
      ]
    }
  ]
}`, req.CurrentLevel, req.TargetLevel, req.LearningGoals, req.FocusAreas, req.SpecialInterests, req.StudyTimePerDay, req.PreferredPace)
}

// buildLessonPrompt creates a detailed prompt for lesson content generation
func (s *OpenAIService) buildLessonPrompt(req LessonGenerationRequest) string {
	return fmt.Sprintf(`Create detailed Japanese lesson content with the following specifications:

Title: %s
Lesson Type: %s
Difficulty: %s
Content Outline: %v
User Level: %s
Focus Areas: %v

Please create comprehensive lesson content including:

1. Lesson Content:
   - Engaging introduction
   - 3-5 main content blocks (mix of text, dialogue, examples, explanations)
   - Clear summary

2. Exercises (5-8 varied exercises):
   - Multiple choice questions
   - Fill-in-the-blank
   - Translation exercises
   - Matching exercises

3. Vocabulary (5-10 new words):
   - Japanese word with kanji/hiragana
   - Reading in hiragana
   - Romaji
   - English meaning
   - Part of speech
   - Example sentence with translation

4. Grammar Points (2-4 points):
   - Grammar pattern
   - Clear explanation
   - Usage guidelines
   - Example sentences

5. Cultural Notes (2-3 insights):
   - Cultural topic
   - Explanation
   - Context for usage

Make content engaging, practical, and appropriate for the difficulty level. Include natural Japanese expressions and real-world applications.

Respond with valid JSON matching the expected structure.`, req.Title, req.LessonType, req.Difficulty, req.ContentOutline, req.UserLevel, req.FocusAreas)
}
