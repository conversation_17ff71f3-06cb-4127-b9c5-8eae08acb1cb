# 道場コーチ (<PERSON><PERSON><PERSON> Coach) - Japanese Learning App

A comprehensive Japanese language learning application with samurai/martial arts theming, AI Sensei integration, and gamification features.

## 🏯 Project Structure

```
japanCoach/
├── backend/                 # Go backend API
│   ├── cmd/
│   │   └── main.go         # Application entry point
│   ├── internal/
│   │   ├── database/       # Database configuration
│   │   ├── handlers/       # HTTP request handlers
│   │   ├── middleware/     # Custom middleware
│   │   └── models/         # Database models
│   ├── go.mod
│   └── go.sum
├── frontend/               # React frontend (to be implemented)
└── README.md
```

## 🚀 Backend Setup

### Prerequisites

- Go 1.21 or higher
- PostgreSQL database (Neon recommended)
- Clerk account with API keys

### Installation

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Install dependencies**
   ```bash
   go mod download
   ```

3. **Set up environment variables**
   Create a `.env` file in the backend directory:
   ```env
   # Server Configuration
   PORT=8080
   GIN_MODE=debug

   # Database Configuration
   DATABASE_URL=your_neon_postgresql_url

   # Clerk Configuration
   CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
   CLERK_SECRET_KEY=your_clerk_secret_key
   ```

4. **Start the server**
   ```bash
   go run cmd/main.go
   ```

   The server will start on `http://localhost:8080`

## 📚 API Documentation

### Base URL
```
http://localhost:8080/api/v1
```

### Authentication
Most endpoints require authentication via Clerk JWT tokens. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Available Endpoints

#### Public Endpoints
- `GET /health` - Health check
- `POST /auth/webhook` - Clerk webhook handler

#### User Management
- `GET /user/profile` - Get current user profile
- `PUT /user/profile` - Update user profile
- `GET /user/progress` - Get user learning progress

#### Lessons
- `GET /lessons` - Get lessons with filters
- `GET /lessons/:id` - Get specific lesson
- `POST /lessons/:id/complete` - Mark lesson as completed

#### Vocabulary
- `GET /vocabulary` - Get vocabulary words
- `GET /vocabulary/:id` - Get specific vocabulary word
- `GET /vocabulary/categories` - Get vocabulary categories
- `GET /vocabulary/jlpt-levels` - Get JLPT levels
- `GET /vocabulary/search?q=term` - Search vocabulary
- `GET /vocabulary/stats` - Get vocabulary statistics

#### Grammar
- `GET /grammar` - Get grammar points
- `GET /grammar/:id` - Get specific grammar point
- `GET /grammar/:id/exercises` - Get practice exercises
- `GET /grammar/categories` - Get grammar categories
- `GET /grammar/search?q=term` - Search grammar
- `GET /grammar/stats` - Get grammar statistics

#### Progress Tracking
- `GET /progress/overview` - Get comprehensive progress overview
- `POST /progress/vocabulary` - Update vocabulary progress
- `POST /progress/grammar` - Update grammar progress

#### AI Sensei
- `POST /sensei/conversation` - Start conversation session
- `POST /sensei/grammar-check` - Check grammar
- `GET /sensei/recommendations` - Get personalized recommendations

#### Achievements
- `GET /achievements` - Get all achievements
- `POST /achievements/:id/unlock` - Unlock achievement

## 🎯 Features

### Gamification System
- **Rank System**: Progress through martial arts belts (白帯 to 師範)
- **Experience Points**: Earn XP through lessons, vocabulary, and achievements
- **Achievement System**: Unlock achievements for various milestones
- **Study Streaks**: Track consecutive study days

### Learning Features
- **Spaced Repetition**: Intelligent vocabulary review scheduling
- **Progress Tracking**: Detailed statistics for all learning areas
- **AI Sensei Integration**: Conversation practice and grammar checking
- **Personalized Recommendations**: Tailored learning suggestions

### Technical Features
- **Comprehensive Logging**: Structured JSON logging with error tracking
- **Rate Limiting**: API protection with intelligent rate limiting
- **Error Handling**: Standardized error responses
- **Authentication**: Secure Clerk-based JWT authentication

## 🧪 Testing

```bash
cd backend
go test ./...
```

## 🚀 Deployment

The backend is ready for deployment with proper environment configuration, logging, and error handling.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request
