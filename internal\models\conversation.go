package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ConversationSession represents an AI Sensei conversation session
type ConversationSession struct {
	ID     uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;not null"`
	User   User      `json:"user" gorm:"foreignKey:UserID"`

	// Session details
	Title       string `json:"title"`
	SenseiType  string `json:"sensei_type" gorm:"not null"` // tanaka, yamada, sato
	Topic       string `json:"topic"` // conversation topic
	Difficulty  string `json:"difficulty" gorm:"default:'beginner'"` // beginner, intermediate, advanced
	
	// Session metadata
	Status      string    `json:"status" gorm:"default:'active'"` // active, completed, paused
	StartedAt   time.Time `json:"started_at"`
	EndedAt     *time.Time `json:"ended_at"`
	Duration    int       `json:"duration" gorm:"default:0"` // in seconds
	
	// Learning objectives
	LearningGoals string `json:"learning_goals"` // JSON array of goals
	FocusAreas    string `json:"focus_areas"` // JSON array of focus areas (grammar, vocabulary, etc.)
	
	// Session results
	MessagesCount     int     `json:"messages_count" gorm:"default:0"`
	CorrectionsCount  int     `json:"corrections_count" gorm:"default:0"`
	NewVocabulary     string  `json:"new_vocabulary"` // JSON array of new words learned
	GrammarCovered    string  `json:"grammar_covered"` // JSON array of grammar points
	OverallScore      float64 `json:"overall_score" gorm:"default:0"` // 0-100
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Messages []ConversationMessage `json:"messages,omitempty"`
}

// ConversationMessage represents a message in a conversation
type ConversationMessage struct {
	ID           uuid.UUID           `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	SessionID    uuid.UUID           `json:"session_id" gorm:"type:uuid;not null"`
	Session      ConversationSession `json:"session" gorm:"foreignKey:SessionID"`

	// Message details
	Role        string `json:"role" gorm:"not null"` // user, sensei, system
	Content     string `json:"content" gorm:"type:text;not null"`
	ContentJP   string `json:"content_jp" gorm:"type:text"` // Japanese version if applicable
	
	// Message metadata
	MessageType string    `json:"message_type" gorm:"default:'text'"` // text, correction, feedback, exercise
	Timestamp   time.Time `json:"timestamp"`
	
	// AI analysis (for user messages)
	GrammarScore     float64 `json:"grammar_score" gorm:"default:0"` // 0-100
	VocabularyScore  float64 `json:"vocabulary_score" gorm:"default:0"` // 0-100
	NaturalnessScore float64 `json:"naturalness_score" gorm:"default:0"` // 0-100
	
	// Corrections and feedback
	Corrections string `json:"corrections"` // JSON array of correction objects
	Feedback    string `json:"feedback"` // JSON object with detailed feedback
	Suggestions string `json:"suggestions"` // JSON array of improvement suggestions
	
	// Audio (if applicable)
	AudioURL string `json:"audio_url"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeCreate sets the ID before creating
func (cs *ConversationSession) BeforeCreate(tx *gorm.DB) error {
	if cs.ID == uuid.Nil {
		cs.ID = uuid.New()
	}
	return nil
}

func (cm *ConversationMessage) BeforeCreate(tx *gorm.DB) error {
	if cm.ID == uuid.Nil {
		cm.ID = uuid.New()
	}
	return nil
}
