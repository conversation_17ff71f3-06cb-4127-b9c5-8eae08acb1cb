import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  Circle, 
  Clock, 
  BookOpen, 
  Target, 
  Sparkles,
  Play,
  Loader2,
  Calendar,
  Trophy
} from 'lucide-react';

interface LessonPlan {
  id: string;
  title: string;
  description: string;
  lesson_type: string;
  difficulty: string;
  estimated_time: number;
  status: string;
  is_generated: boolean;
  order: number;
}

interface Milestone {
  id: string;
  title: string;
  description: string;
  order: number;
  estimated_days: number;
  status: string;
  learning_objectives: string;
  skills_to_acquire: string;
  lesson_plans: LessonPlan[];
}

interface Roadmap {
  id: string;
  title: string;
  description: string;
  target_level: string;
  estimated_duration: number;
  completion_rate: number;
  status: string;
  milestones: Milestone[];
}

interface RoadmapDisplayProps {
  roadmap: Roadmap;
  onGenerateLesson: (lessonPlanId: string) => void;
  onStartLesson: (lessonPlanId: string) => void;
  isGeneratingLesson?: string;
}

export const RoadmapDisplay: React.FC<RoadmapDisplayProps> = ({ 
  roadmap, 
  onGenerateLesson, 
  onStartLesson,
  isGeneratingLesson 
}) => {
  const [expandedMilestone, setExpandedMilestone] = useState<string | null>(null);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-blue-500" />;
      default:
        return <Circle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'generating':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getLessonTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'vocabulary':
        return '📝';
      case 'grammar':
        return '📖';
      case 'conversation':
        return '💬';
      case 'writing':
        return '✍️';
      case 'reading':
        return '📚';
      case 'listening':
        return '👂';
      case 'culture':
        return '⛩️';
      default:
        return '📋';
    }
  };

  const parseJsonString = (jsonString: string): string[] => {
    try {
      return JSON.parse(jsonString);
    } catch {
      return [];
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Roadmap Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl flex items-center gap-2">
                <Target className="h-6 w-6 text-blue-500" />
                {roadmap.title}
              </CardTitle>
              <p className="text-muted-foreground mt-2">{roadmap.description}</p>
            </div>
            <Badge variant="outline" className="text-lg px-3 py-1">
              {roadmap.target_level}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                <strong>{roadmap.estimated_duration}</strong> days estimated
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Trophy className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                <strong>{roadmap.completion_rate.toFixed(1)}%</strong> completed
              </span>
            </div>
            <div className="flex items-center gap-2">
              <BookOpen className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                <strong>{roadmap.milestones.length}</strong> milestones
              </span>
            </div>
          </div>
          <Progress value={roadmap.completion_rate} className="h-2" />
        </CardContent>
      </Card>

      {/* Milestones */}
      <div className="space-y-4">
        {roadmap.milestones
          .sort((a, b) => a.order - b.order)
          .map((milestone, index) => (
            <Card key={milestone.id} className="overflow-hidden">
              <CardHeader 
                className="cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => setExpandedMilestone(
                  expandedMilestone === milestone.id ? null : milestone.id
                )}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(milestone.status)}
                    <div>
                      <CardTitle className="text-lg">
                        Milestone {milestone.order}: {milestone.title}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">
                        {milestone.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(milestone.status)}>
                      {milestone.status.replace('_', ' ')}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {milestone.estimated_days} days
                    </span>
                  </div>
                </div>
              </CardHeader>

              {expandedMilestone === milestone.id && (
                <CardContent className="pt-0">
                  {/* Learning Objectives */}
                  <div className="mb-4">
                    <h4 className="font-semibold mb-2">Learning Objectives:</h4>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      {parseJsonString(milestone.learning_objectives).map((objective, idx) => (
                        <li key={idx}>{objective}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Skills to Acquire */}
                  <div className="mb-4">
                    <h4 className="font-semibold mb-2">Skills to Acquire:</h4>
                    <div className="flex flex-wrap gap-2">
                      {parseJsonString(milestone.skills_to_acquire).map((skill, idx) => (
                        <Badge key={idx} variant="secondary">{skill}</Badge>
                      ))}
                    </div>
                  </div>

                  {/* Lesson Plans */}
                  <div>
                    <h4 className="font-semibold mb-3">Lesson Plans:</h4>
                    <div className="grid gap-3">
                      {milestone.lesson_plans
                        .sort((a, b) => a.order - b.order)
                        .map((lesson) => (
                          <Card key={lesson.id} className="border-l-4 border-l-blue-200">
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <span className="text-2xl">
                                    {getLessonTypeIcon(lesson.lesson_type)}
                                  </span>
                                  <div>
                                    <h5 className="font-medium">{lesson.title}</h5>
                                    <p className="text-sm text-muted-foreground">
                                      {lesson.description}
                                    </p>
                                    <div className="flex items-center gap-2 mt-1">
                                      <Badge 
                                        variant="outline" 
                                        className={getDifficultyColor(lesson.difficulty)}
                                      >
                                        {lesson.difficulty}
                                      </Badge>
                                      <Badge variant="outline">
                                        {lesson.lesson_type}
                                      </Badge>
                                      <span className="text-xs text-muted-foreground">
                                        {lesson.estimated_time} min
                                      </span>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                  {lesson.is_generated ? (
                                    <Button
                                      onClick={() => onStartLesson(lesson.id)}
                                      size="sm"
                                      className="bg-green-600 hover:bg-green-700"
                                    >
                                      <Play className="h-4 w-4 mr-1" />
                                      Start Lesson
                                    </Button>
                                  ) : (
                                    <Button
                                      onClick={() => onGenerateLesson(lesson.id)}
                                      disabled={isGeneratingLesson === lesson.id}
                                      size="sm"
                                      variant="outline"
                                    >
                                      {isGeneratingLesson === lesson.id ? (
                                        <>
                                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                          Generating...
                                        </>
                                      ) : (
                                        <>
                                          <Sparkles className="h-4 w-4 mr-1" />
                                          Generate Lesson
                                        </>
                                      )}
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
      </div>
    </div>
  );
};
