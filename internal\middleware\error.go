package middleware

import (
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
)

// ErrorResponse represents a standardized error response
type ErrorResponse struct {
	Error   ErrorDetail `json:"error"`
	Success bool        `json:"success"`
}

// ErrorDetail contains detailed error information
type ErrorDetail struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}

// ErrorHandler middleware handles panics and standardizes error responses
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// Log the panic
				LogError(c, err.(error), "Panic recovered", map[string]interface{}{
					"stack_trace": string(debug.Stack()),
				})

				// Return standardized error response
				c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error: ErrorDetail{
						Code:    "INTERNAL_SERVER_ERROR",
						Message: "An unexpected error occurred. Please try again later.",
					},
					Success: false,
				})
				c.Abort()
			}
		}()

		c.Next()

		// Handle errors that were set during request processing
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			// Determine status code based on error type
			statusCode := http.StatusInternalServerError
			errorCode := "INTERNAL_SERVER_ERROR"

			switch err.Type {
			case gin.ErrorTypeBind:
				statusCode = http.StatusBadRequest
				errorCode = "VALIDATION_ERROR"
			case gin.ErrorTypePublic:
				statusCode = http.StatusBadRequest
				errorCode = "BAD_REQUEST"
			}

			// Log the error
			LogError(c, err.Err, "Request error", map[string]interface{}{
				"error_type": string(rune(err.Type)),
			})

			// Return standardized error response
			c.JSON(statusCode, ErrorResponse{
				Error: ErrorDetail{
					Code:    errorCode,
					Message: err.Error(),
				},
				Success: false,
			})
		}
	}
}

// ValidationError creates a validation error response
func ValidationError(c *gin.Context, message string, details interface{}) {
	c.JSON(http.StatusBadRequest, ErrorResponse{
		Error: ErrorDetail{
			Code:    "VALIDATION_ERROR",
			Message: message,
			Details: details,
		},
		Success: false,
	})
}

// NotFoundError creates a not found error response
func NotFoundError(c *gin.Context, resource string) {
	c.JSON(http.StatusNotFound, ErrorResponse{
		Error: ErrorDetail{
			Code:    "NOT_FOUND",
			Message: resource + " not found",
		},
		Success: false,
	})
}

// UnauthorizedError creates an unauthorized error response
func UnauthorizedError(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, ErrorResponse{
		Error: ErrorDetail{
			Code:    "UNAUTHORIZED",
			Message: message,
		},
		Success: false,
	})
}

// ForbiddenError creates a forbidden error response
func ForbiddenError(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, ErrorResponse{
		Error: ErrorDetail{
			Code:    "FORBIDDEN",
			Message: message,
		},
		Success: false,
	})
}

// ConflictError creates a conflict error response
func ConflictError(c *gin.Context, message string) {
	c.JSON(http.StatusConflict, ErrorResponse{
		Error: ErrorDetail{
			Code:    "CONFLICT",
			Message: message,
		},
		Success: false,
	})
}

// InternalServerError creates an internal server error response
func InternalServerError(c *gin.Context, message string) {
	c.JSON(http.StatusInternalServerError, ErrorResponse{
		Error: ErrorDetail{
			Code:    "INTERNAL_SERVER_ERROR",
			Message: message,
		},
		Success: false,
	})
}

// DatabaseError creates a database error response
func DatabaseError(c *gin.Context, err error) {
	LogError(c, err, "Database error")

	c.JSON(http.StatusInternalServerError, ErrorResponse{
		Error: ErrorDetail{
			Code:    "DATABASE_ERROR",
			Message: "A database error occurred. Please try again later.",
		},
		Success: false,
	})
}

// SuccessResponse represents a standardized success response
type SuccessResponse struct {
	Data    interface{} `json:"data"`
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
}

// SuccessWithData creates a success response with data
func SuccessWithData(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, SuccessResponse{
		Data:    data,
		Success: true,
	})
}

// SuccessWithMessage creates a success response with a message
func SuccessWithMessage(c *gin.Context, message string) {
	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: message,
	})
}

// CreatedWithData creates a 201 response with data
func CreatedWithData(c *gin.Context, data interface{}) {
	c.JSON(http.StatusCreated, SuccessResponse{
		Data:    data,
		Success: true,
	})
}

// RateLimitError creates a rate limit error response
func RateLimitError(c *gin.Context) {
	c.JSON(http.StatusTooManyRequests, ErrorResponse{
		Error: ErrorDetail{
			Code:    "RATE_LIMIT_EXCEEDED",
			Message: "Too many requests. Please try again later.",
		},
		Success: false,
	})
}

// ServiceUnavailableError creates a service unavailable error response
func ServiceUnavailableError(c *gin.Context, service string) {
	c.JSON(http.StatusServiceUnavailable, ErrorResponse{
		Error: ErrorDetail{
			Code:    "SERVICE_UNAVAILABLE",
			Message: service + " is currently unavailable. Please try again later.",
		},
		Success: false,
	})
}
