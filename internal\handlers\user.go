package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"japancoach-backend/internal/middleware"
	"japancoach-backend/internal/models"
)

type UserHandler struct {
	db *gorm.DB
}

func NewUserHandler(db *gorm.DB) *UserHandler {
	return &UserHandler{db: db}
}

// HandleClerkWebhook handles Clerk webhook events for user management
func (h *UserHandler) HandleClerkWebhook(c *gin.Context) {
	var payload map[string]interface{}
	if err := c.ShouldBindJSON(&payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON payload"})
		return
	}

	eventType, ok := payload["type"].(string)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing event type"})
		return
	}

	switch eventType {
	case "user.created":
		h.handleUserCreated(c, payload)
	case "user.updated":
		h.handleUserUpdated(c, payload)
	case "user.deleted":
		h.handleUserDeleted(c, payload)
	default:
		c.J<PERSON>(http.StatusOK, gin.H{"message": "Event type not handled"})
	}
}

func (h *UserHandler) handleUserCreated(c *gin.Context, payload map[string]interface{}) {
	data, ok := payload["data"].(map[string]interface{})
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}

	user := models.User{
		ClerkID:   data["id"].(string),
		Email:     getStringFromData(data, "email_addresses.0.email_address"),
		FirstName: getStringFromData(data, "first_name"),
		LastName:  getStringFromData(data, "last_name"),
		Username:  getStringFromData(data, "username"),
	}

	if err := h.db.Create(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// Create initial user progress
	progress := models.UserProgress{
		UserID: user.ID,
	}
	h.db.Create(&progress)

	c.JSON(http.StatusOK, gin.H{"message": "User created successfully"})
}

func (h *UserHandler) handleUserUpdated(c *gin.Context, payload map[string]interface{}) {
	data, ok := payload["data"].(map[string]interface{})
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}

	clerkID := data["id"].(string)
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Update user fields
	user.Email = getStringFromData(data, "email_addresses.0.email_address")
	user.FirstName = getStringFromData(data, "first_name")
	user.LastName = getStringFromData(data, "last_name")
	user.Username = getStringFromData(data, "username")

	if err := h.db.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
}

func (h *UserHandler) handleUserDeleted(c *gin.Context, payload map[string]interface{}) {
	data, ok := payload["data"].(map[string]interface{})
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}

	clerkID := data["id"].(string)
	if err := h.db.Where("clerk_id = ?", clerkID).Delete(&models.User{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

// GetProfile returns the current user's profile
func (h *UserHandler) GetProfile(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var user models.User
	if err := h.db.Preload("Progress").Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateProfile updates the current user's profile
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	var updateData struct {
		JapaneseLevel   string `json:"japanese_level"`
		PreferredSensei string `json:"preferred_sensei"`
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Update allowed fields
	if updateData.JapaneseLevel != "" {
		user.JapaneseLevel = updateData.JapaneseLevel
	}
	if updateData.PreferredSensei != "" {
		user.PreferredSensei = updateData.PreferredSensei
	}

	// Update last study date and streak
	now := time.Now()
	if user.LastStudyDate != nil {
		// Check if it's a new day
		if user.LastStudyDate.Day() != now.Day() {
			// Check if it's consecutive days
			yesterday := now.AddDate(0, 0, -1)
			if user.LastStudyDate.Day() == yesterday.Day() {
				user.StudyStreak++
			} else {
				user.StudyStreak = 1
			}
		}
	} else {
		user.StudyStreak = 1
	}
	user.LastStudyDate = &now

	if err := h.db.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update profile"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// GetUserProgress returns the current user's learning progress
func (h *UserHandler) GetUserProgress(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	var progress models.UserProgress
	if err := h.db.Where("user_id = ?", user.ID).First(&progress).Error; err != nil {
		// Create initial progress if it doesn't exist
		progress = models.UserProgress{
			UserID: user.ID,
		}
		h.db.Create(&progress)
	}

	c.JSON(http.StatusOK, progress)
}

// Helper function to safely extract string values from nested map data
func getStringFromData(data map[string]interface{}, key string) string {
	if val, ok := data[key]; ok && val != nil {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}
