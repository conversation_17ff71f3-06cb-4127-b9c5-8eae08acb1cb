package middleware

import (
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/clerk/clerk-sdk-go/v2"
	"github.com/clerk/clerk-sdk-go/v2/jwt"
	"github.com/gin-gonic/gin"
)

// ClerkAuth middleware validates Clerk JWT tokens
func ClerkAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the authorization header
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header is required",
			})
			c.Abort()
			return
		}

		// Extract the token from "Bearer <token>"
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		token := tokenParts[1]

		// Verify the JWT token with Clerk
		claims, err := jwt.Verify(c.Request.Context(), &jwt.VerifyParams{
			Token: token,
		})
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid or expired token",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		// Extract user information from claims
		userID := claims.Subject
		if userID == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token: missing user ID",
			})
			c.Abort()
			return
		}

		// Set user information in context for use in handlers
		c.Set("clerk_user_id", userID)
		c.Set("clerk_claims", claims)

		c.Next()
	}
}

// GetUserIDFromContext extracts the Clerk user ID from the Gin context
func GetUserIDFromContext(c *gin.Context) (string, bool) {
	userID, exists := c.Get("clerk_user_id")
	if !exists {
		return "", false
	}

	userIDStr, ok := userID.(string)
	return userIDStr, ok
}

// GetClaimsFromContext extracts the Clerk claims from the Gin context
func GetClaimsFromContext(c *gin.Context) (*clerk.SessionClaims, bool) {
	claims, exists := c.Get("clerk_claims")
	if !exists {
		return nil, false
	}

	claimsObj, ok := claims.(*clerk.SessionClaims)
	return claimsObj, ok
}

// InitializeClerk initializes the Clerk client
func InitializeClerk() error {
	secretKey := os.Getenv("CLERK_SECRET_KEY")
	if secretKey == "" {
		return fmt.Errorf("CLERK_SECRET_KEY environment variable is required")
	}

	clerk.SetKey(secretKey)
	return nil
}
