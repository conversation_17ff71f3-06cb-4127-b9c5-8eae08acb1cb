import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Sword, Target, BookOpen, Users, Award, TrendingUp, Calendar, Clock, Star, ChevronRight } from 'lucide-react';

export default function Dashboard() {
  const navigate = useNavigate();
  const [selectedDiscipline, setSelectedDiscipline] = useState(null);
  
  const learningStats = {
    streak: 7,
    totalLessons: 42,
    wordsLearned: 156,
    level: 'Beginner'
  };
  
  const aiAgents = [
    {
      id: 'sakura',
      name: '<PERSON><PERSON>sensei',
      role: 'Conversation Master',
      status: 'online',
      avatar: '🌸',
      color: 'from-pink-400 to-rose-500',
      description: 'Ready to practice conversations!'
    },
    {
      id: 'kenji',
      name: '<PERSON><PERSON><PERSON>kun',
      role: 'Grammar Guru',
      status: 'online',
      avatar: '📚',
      color: 'from-blue-400 to-indigo-500',
      description: 'Let\'s master grammar together!'
    },
    {
      id: 'yuki',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      role: 'Writing Wizard',
      status: 'busy',
      avatar: '✨',
      color: 'from-purple-400 to-pink-500',
      description: 'Helping other students write beautifully'
    }
  ];
  
  const learningModules = [
    {
      id: 'vocabulary',
      title: 'Vocabulary Builder',
      icon: '📝',
      progress: 65,
      lessons: 12,
      color: 'from-green-400 to-emerald-500'
    },
    {
      id: 'grammar',
      title: 'Grammar Fundamentals',
      icon: '📖',
      progress: 40,
      lessons: 8,
      color: 'from-blue-400 to-cyan-500'
    },
    {
      id: 'conversation',
      title: 'Conversation Practice',
      icon: '💬',
      progress: 30,
      lessons: 5,
      color: 'from-purple-400 to-violet-500'
    },
    {
      id: 'writing',
      title: 'Writing Skills',
      icon: '✍️',
      progress: 20,
      lessons: 3,
      color: 'from-orange-400 to-red-500'
    },
    {
      id: 'kanji',
      title: 'Kanji Mastery',
      icon: '漢',
      progress: 15,
      lessons: 2,
      color: 'from-indigo-400 to-purple-500'
    },
    {
      id: 'culture',
      title: 'Cultural Insights',
      icon: '⛩️',
      progress: 10,
      lessons: 1,
      color: 'from-pink-400 to-rose-500'
    }
  ];
  
  const recommendations = [
    {
      title: 'Daily Vocabulary Review',
      description: 'Review 10 words you learned this week',
      time: '5 min',
      priority: 'high'
    },
    {
      title: 'Grammar Practice: Particles',
      description: 'Master は, が, and を particles',
      time: '15 min',
      priority: 'medium'
    },
    {
      title: 'Conversation with Sakura-sensei',
      description: 'Practice introducing yourself',
      time: '10 min',
      priority: 'high'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-slate-800 to-gray-900">
      {/* Navigation */}
      <nav className="bg-black/90 backdrop-blur-sm border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Sword className="w-8 h-8 text-red-600" />
              <span className="text-2xl font-serif-jp font-bold text-white">修行場</span>
              <span className="text-lg font-sans-jp text-gray-300">Training Grounds</span>
            </div>
            <div className="flex items-center space-x-6">
              <button className="text-gray-300 hover:text-white transition-colors font-sans-jp">Profile</button>
              <button className="text-gray-300 hover:text-white transition-colors font-sans-jp">Settings</button>
              <button 
                onClick={() => navigate('/')}
                className="samurai-button"
              >
                Return to Dojo
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-8 text-center">
          <div className="paper-texture p-8 border-2 border-gray-700 mb-6">
            <h1 className="text-4xl font-serif-jp font-bold text-gray-900 mb-2 brush-stroke">修行者への挨拶</h1>
            <p className="text-xl font-sans-jp text-gray-700">Welcome back, dedicated student. Your path to mastery continues.</p>
          </div>
        </div>

        {/* Progress Overview */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          {[
            { label: 'Training Days', value: '12', icon: <Calendar className="w-8 h-8" />, rank: '継続' },
            { label: 'Kanji Mastered', value: '247', icon: <BookOpen className="w-8 h-8" />, rank: '学習' },
            { label: 'Dialogues', value: '18', icon: <Users className="w-8 h-8" />, rank: '会話' },
            { label: 'Mastery Level', value: '68%', icon: <Target className="w-8 h-8" />, rank: '進歩' }
          ].map((stat, index) => (
            <div key={index} className="samurai-card p-6 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="text-gray-800">{stat.icon}</div>
                <div className="w-12 h-12 bg-gray-800 border border-gray-600 flex items-center justify-center">
                  <span className="text-white font-serif-jp text-sm">{stat.rank}</span>
                </div>
              </div>
              <h3 className="text-gray-900 font-sans-jp font-semibold mb-1">{stat.label}</h3>
              <p className="text-2xl font-serif-jp font-bold text-gray-900">{stat.value}</p>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Sensei Council */}
          <div className="lg:col-span-1">
            <h2 className="text-3xl font-serif-jp font-bold text-white mb-6 brush-stroke">先生評議会</h2>
            <div className="space-y-4">
              {[
                {
                  name: '田中先生',
                  romanji: 'Tanaka-sensei',
                  role: 'Master of Conversation',
                  status: 'Ready for Training',
                  rank: '九段',
                  specialization: 'Dialogue & Etiquette',
                  description: 'Refine your conversational skills through respectful discourse'
                },
                {
                  name: '山田先生',
                  romanji: 'Yamada-sensei',
                  role: 'Guardian of Grammar',
                  status: 'Ready for Training',
                  rank: '八段',
                  specialization: 'Structure & Form',
                  description: 'Build unshakeable grammatical foundations through discipline'
                },
                {
                  name: '佐藤先生',
                  romanji: 'Satō-sensei',
                  role: 'Keeper of Written Arts',
                  status: 'Ready for Training',
                  rank: '七段',
                  specialization: 'Calligraphy & Composition',
                  description: 'Master the elegant art of Japanese written expression'
                }
              ].map((sensei, index) => (
                <div 
                  key={index}
                  className={`bg-gray-800 border-2 border-gray-700 p-6 cursor-pointer transition-all duration-300 hover:border-red-600 hover:shadow-lg ${
                    selectedDiscipline === index ? 'border-red-500 shadow-red-500/20' : ''
                  }`}
                  onClick={() => setSelectedDiscipline(selectedDiscipline === index ? null : index)}
                >
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-700 border-2 border-gray-600 flex items-center justify-center">
                      <span className="text-white font-serif-jp text-lg">{sensei.rank}</span>
                    </div>
                    <h3 className="text-xl font-serif-jp font-bold text-white mb-1">{sensei.name}</h3>
                    <p className="text-gray-400 font-sans-jp text-sm mb-1">{sensei.romanji}</p>
                    <p className="text-red-400 font-sans-jp mb-2">{sensei.role}</p>
                    <div className="flex items-center justify-center mb-3">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                      <span className="text-green-300 text-sm font-sans-jp">{sensei.status}</span>
                    </div>
                    <p className="text-gray-300 text-sm font-sans-jp mb-2">{sensei.specialization}</p>
                    <p className="text-gray-400 text-xs font-sans-jp mb-4">{sensei.description}</p>
                    <button className="samurai-button w-full">
                      Begin Training
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Training Modules */}
          <div className="lg:col-span-2">
            <h2 className="text-3xl font-serif-jp font-bold text-white mb-6 brush-stroke">修行科目</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {[
                { title: 'Hiragana Foundation', progress: 85, character: 'あ', level: '初級' },
                { title: 'Katakana Mastery', progress: 72, character: 'カ', level: '初級' },
                { title: 'Kanji Discipline', progress: 45, character: '漢', level: '中級' },
                { title: 'Grammar Patterns', progress: 60, character: '文', level: '中級' }
              ].map((module, index) => (
                <div key={index} className="samurai-card p-6 hover:shadow-lg transition-all duration-300 cursor-pointer group">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-800 border-2 border-gray-600 flex items-center justify-center group-hover:border-red-600 transition-colors duration-300">
                      <span className="text-2xl font-serif-jp text-white">{module.character}</span>
                    </div>
                    <h3 className="text-lg font-sans-jp font-semibold text-gray-900 mb-2">{module.title}</h3>
                    <div className="w-full bg-gray-300 h-2 mb-2">
                      <div 
                        className="h-2 bg-gradient-to-r from-gray-800 to-red-600"
                        style={{ width: `${module.progress}%` }}
                      ></div>
                    </div>
                    <p className="text-gray-700 text-sm font-sans-jp mb-1">{module.progress}% Mastered</p>
                    <p className="text-gray-600 text-xs font-serif-jp">{module.level}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Training Guidance */}
            <h3 className="text-xl font-serif-jp font-bold text-white mb-4 flex items-center">
              <Target className="w-6 h-6 mr-2 text-red-400" />
              修行指導
            </h3>
            <div className="bg-gray-800 border-2 border-gray-700 p-8">
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h4 className="text-lg font-serif-jp font-bold text-white mb-4 flex items-center">
                    <Target className="w-5 h-5 mr-2 text-red-400" />
                    Focus Areas
                  </h4>
                  <div className="space-y-3">
                    {[
                      'Refine particle mastery (は, が, を)',
                      'Strengthen past tense foundations',
                      'Expand family terminology knowledge'
                    ].map((item, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <ChevronRight className="w-4 h-4 text-red-400" />
                        <span className="text-gray-300 font-sans-jp">{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="text-lg font-serif-jp font-bold text-white mb-4 flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-red-400" />
                    Next Achievements
                  </h4>
                  <div className="space-y-3">
                    {[
                      'Complete 5 formal dialogues',
                      'Master 20 essential kanji',
                      'Perfect honorific expressions'
                    ].map((item, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <ChevronRight className="w-4 h-4 text-red-400" />
                        <span className="text-gray-300 font-sans-jp">{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Training */}
        <div className="mt-8">
          <h3 className="text-3xl font-serif-jp font-bold text-white mb-6 brush-stroke">即座修行</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { title: 'Daily Challenge', icon: <Star className="w-8 h-8" />, discipline: '日課' },
              { title: 'Memory Cards', icon: <BookOpen className="w-8 h-8" />, discipline: '記憶' },
              { title: 'Speech Training', icon: <Users className="w-8 h-8" />, discipline: '発音' },
              { title: 'Calligraphy', icon: <Target className="w-8 h-8" />, discipline: '書道' }
            ].map((action, index) => (
              <button 
                key={index}
                className="samurai-card p-6 hover:shadow-lg transition-all duration-300 group"
              >
                <div className="text-center">
                  <div className="text-gray-800 mb-3 group-hover:text-red-600 transition-colors duration-300">{action.icon}</div>
                  <h4 className="font-sans-jp font-semibold text-gray-900 mb-1">{action.title}</h4>
                  <p className="font-serif-jp text-sm text-gray-600">{action.discipline}</p>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}