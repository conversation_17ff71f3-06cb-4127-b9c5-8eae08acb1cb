package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"japancoach-backend/internal/middleware"
	"japancoach-backend/internal/models"
)

type LessonHandler struct {
	db *gorm.DB
}

func NewLessonHandler(db *gorm.DB) *LessonHandler {
	return &LessonHandler{db: db}
}

// GetLessons returns a list of lessons based on user's level and preferences
func (h *LessonHandler) GetLessons(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user to determine their level
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Parse query parameters
	level := c.DefaultQuery("level", user.JapaneseLevel)
	category := c.Query("category")
	sensei := c.DefaultQuery("sensei", user.PreferredSensei)
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)

	// Build query
	query := h.db.Model(&models.Lesson{}).
		Where("is_published = ?", true).
		Where("level = ?", level)

	if category != "" {
		query = query.Where("category = ?", category)
	}

	if sensei != "" {
		query = query.Where("assigned_sensei = ?", sensei)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get lessons with content
	var lessons []models.Lesson
	if err := query.
		Preload("Content").
		Preload("Vocabulary").
		Preload("Grammar").
		Order("order ASC, created_at ASC").
		Limit(limit).
		Offset(offset).
		Find(&lessons).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch lessons"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"lessons": lessons,
		"total":   total,
		"limit":   limit,
		"offset":  offset,
	})
}

// GetLesson returns a specific lesson by ID
func (h *LessonHandler) GetLesson(c *gin.Context) {
	_, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	lessonID := c.Param("id")
	lessonUUID, err := uuid.Parse(lessonID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	var lesson models.Lesson
	if err := h.db.
		Preload("Content", func(db *gorm.DB) *gorm.DB {
			return db.Order("order ASC")
		}).
		Preload("Vocabulary").
		Preload("Grammar").
		Where("id = ? AND is_published = ?", lessonUUID, true).
		First(&lesson).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Lesson not found"})
		return
	}

	c.JSON(http.StatusOK, lesson)
}

// CompleteLesson marks a lesson as completed for the user
func (h *LessonHandler) CompleteLesson(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	lessonID := c.Param("id")
	lessonUUID, err := uuid.Parse(lessonID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	// Verify lesson exists
	var lesson models.Lesson
	if err := h.db.Where("id = ?", lessonUUID).First(&lesson).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Lesson not found"})
		return
	}

	// Parse completion data
	var completionData struct {
		Score          float64 `json:"score"`
		TimeSpent      int     `json:"time_spent"`      // in seconds
		ExerciseScores string  `json:"exercise_scores"` // JSON string
	}

	if err := c.ShouldBindJSON(&completionData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid completion data"})
		return
	}

	// Update user progress
	var progress models.UserProgress
	if err := h.db.Where("user_id = ?", user.ID).First(&progress).Error; err != nil {
		// Create progress if it doesn't exist
		progress = models.UserProgress{
			UserID: user.ID,
		}
	}

	progress.LessonsCompleted++

	// Update skill levels based on lesson category
	switch lesson.Category {
	case "reading":
		if progress.ReadingLevel < 100 {
			progress.ReadingLevel = min(100, progress.ReadingLevel+5)
		}
	case "writing":
		if progress.WritingLevel < 100 {
			progress.WritingLevel = min(100, progress.WritingLevel+5)
		}
	case "conversation":
		if progress.SpeakingLevel < 100 {
			progress.SpeakingLevel = min(100, progress.SpeakingLevel+5)
		}
		if progress.ListeningLevel < 100 {
			progress.ListeningLevel = min(100, progress.ListeningLevel+3)
		}
	case "grammar":
		// Grammar lessons improve all skills slightly
		progress.ReadingLevel = min(100, progress.ReadingLevel+2)
		progress.WritingLevel = min(100, progress.WritingLevel+3)
	}

	if err := h.db.Save(&progress).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update progress"})
		return
	}

	// Update user stats
	user.TotalStudyTime += completionData.TimeSpent / 60 // convert to minutes
	user.ExperiencePoints += calculateExperiencePoints(completionData.Score, lesson.DifficultyRating)

	// Update rank based on experience points
	user.CurrentRank = calculateRank(user.ExperiencePoints)

	if err := h.db.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user stats"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":           "Lesson completed successfully",
		"experience_gained": calculateExperiencePoints(completionData.Score, lesson.DifficultyRating),
		"new_rank":          user.CurrentRank,
		"progress":          progress,
	})
}

// Helper functions
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func calculateExperiencePoints(score float64, difficulty int) int {
	basePoints := 10
	difficultyMultiplier := float64(difficulty)
	scoreMultiplier := score / 100.0

	return int(float64(basePoints) * difficultyMultiplier * scoreMultiplier)
}

func calculateRank(experiencePoints int) string {
	ranks := []struct {
		points int
		rank   string
	}{
		{0, "白帯"},    // White belt
		{100, "黄帯"},  // Yellow belt
		{300, "橙帯"},  // Orange belt
		{600, "緑帯"},  // Green belt
		{1000, "青帯"}, // Blue belt
		{1500, "茶帯"}, // Brown belt
		{2500, "黒帯"}, // Black belt
		{5000, "師範"}, // Master
	}

	currentRank := ranks[0].rank
	for _, rank := range ranks {
		if experiencePoints >= rank.points {
			currentRank = rank.rank
		} else {
			break
		}
	}

	return currentRank
}
