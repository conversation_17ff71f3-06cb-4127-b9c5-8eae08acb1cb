import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, Sparkles, Target, Clock, BookOpen } from 'lucide-react';

interface RoadmapGeneratorProps {
  onRoadmapGenerated: (roadmap: any) => void;
}

export const RoadmapGenerator: React.FC<RoadmapGeneratorProps> = ({ onRoadmapGenerated }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [formData, setFormData] = useState({
    targetLevel: '',
    learningGoals: [] as string[],
    focusAreas: [] as string[],
    specialInterests: [] as string[],
    studyTimePerDay: 30,
    preferredPace: 'moderate'
  });

  const targetLevels = [
    { value: 'beginner', label: 'Beginner (N5-N4)' },
    { value: 'intermediate', label: 'Intermediate (N3-N2)' },
    { value: 'advanced', label: 'Advanced (N1)' },
    { value: 'native', label: 'Native-like fluency' }
  ];

  const learningGoalOptions = [
    'Pass JLPT exam',
    'Travel to Japan',
    'Business communication',
    'Academic study',
    'Anime/Manga comprehension',
    'Cultural understanding',
    'Daily conversation',
    'Professional development'
  ];

  const focusAreaOptions = [
    'conversation',
    'grammar',
    'vocabulary',
    'writing',
    'reading',
    'listening',
    'kanji',
    'culture'
  ];

  const specialInterestOptions = [
    'Business Japanese',
    'Travel phrases',
    'Anime/Manga',
    'Traditional culture',
    'Modern culture',
    'Technology',
    'Food culture',
    'History',
    'Literature',
    'Pop culture'
  ];

  const paceOptions = [
    { value: 'slow', label: 'Slow & Steady (Relaxed pace)' },
    { value: 'moderate', label: 'Moderate (Balanced approach)' },
    { value: 'fast', label: 'Fast Track (Accelerated learning)' },
    { value: 'intensive', label: 'Intensive (Immersive experience)' }
  ];

  const handleArrayChange = (field: keyof typeof formData, value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked 
        ? [...(prev[field] as string[]), value]
        : (prev[field] as string[]).filter(item => item !== value)
    }));
  };

  const handleGenerateRoadmap = async () => {
    if (!formData.targetLevel || formData.learningGoals.length === 0 || formData.focusAreas.length === 0) {
      alert('Please fill in all required fields');
      return;
    }

    setIsGenerating(true);
    
    try {
      const response = await fetch('/api/v1/roadmaps/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error('Failed to generate roadmap');
      }

      const data = await response.json();
      onRoadmapGenerated(data.roadmap);
    } catch (error) {
      console.error('Error generating roadmap:', error);
      alert('Failed to generate roadmap. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2 text-2xl">
          <Sparkles className="h-6 w-6 text-yellow-500" />
          Generate Your Learning Roadmap
          <Sparkles className="h-6 w-6 text-yellow-500" />
        </CardTitle>
        <p className="text-muted-foreground">
          Create a personalized Japanese learning journey tailored to your goals and preferences
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Target Level */}
        <div className="space-y-2">
          <Label htmlFor="targetLevel" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Target Level *
          </Label>
          <Select value={formData.targetLevel} onValueChange={(value) => setFormData(prev => ({ ...prev, targetLevel: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select your target level" />
            </SelectTrigger>
            <SelectContent>
              {targetLevels.map(level => (
                <SelectItem key={level.value} value={level.value}>
                  {level.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Learning Goals */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            Learning Goals * (Select at least one)
          </Label>
          <div className="grid grid-cols-2 gap-2">
            {learningGoalOptions.map(goal => (
              <div key={goal} className="flex items-center space-x-2">
                <Checkbox
                  id={`goal-${goal}`}
                  checked={formData.learningGoals.includes(goal)}
                  onCheckedChange={(checked) => handleArrayChange('learningGoals', goal, checked as boolean)}
                />
                <Label htmlFor={`goal-${goal}`} className="text-sm">{goal}</Label>
              </div>
            ))}
          </div>
        </div>

        {/* Focus Areas */}
        <div className="space-y-2">
          <Label>Focus Areas * (Select at least one)</Label>
          <div className="grid grid-cols-4 gap-2">
            {focusAreaOptions.map(area => (
              <div key={area} className="flex items-center space-x-2">
                <Checkbox
                  id={`focus-${area}`}
                  checked={formData.focusAreas.includes(area)}
                  onCheckedChange={(checked) => handleArrayChange('focusAreas', area, checked as boolean)}
                />
                <Label htmlFor={`focus-${area}`} className="text-sm capitalize">{area}</Label>
              </div>
            ))}
          </div>
        </div>

        {/* Special Interests */}
        <div className="space-y-2">
          <Label>Special Interests (Optional)</Label>
          <div className="grid grid-cols-2 gap-2">
            {specialInterestOptions.map(interest => (
              <div key={interest} className="flex items-center space-x-2">
                <Checkbox
                  id={`interest-${interest}`}
                  checked={formData.specialInterests.includes(interest)}
                  onCheckedChange={(checked) => handleArrayChange('specialInterests', interest, checked as boolean)}
                />
                <Label htmlFor={`interest-${interest}`} className="text-sm">{interest}</Label>
              </div>
            ))}
          </div>
        </div>

        {/* Study Time */}
        <div className="space-y-2">
          <Label htmlFor="studyTime" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Daily Study Time (minutes)
          </Label>
          <Input
            id="studyTime"
            type="number"
            min="15"
            max="240"
            value={formData.studyTimePerDay}
            onChange={(e) => setFormData(prev => ({ ...prev, studyTimePerDay: parseInt(e.target.value) || 30 }))}
          />
        </div>

        {/* Preferred Pace */}
        <div className="space-y-2">
          <Label htmlFor="pace">Preferred Learning Pace</Label>
          <Select value={formData.preferredPace} onValueChange={(value) => setFormData(prev => ({ ...prev, preferredPace: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select your preferred pace" />
            </SelectTrigger>
            <SelectContent>
              {paceOptions.map(pace => (
                <SelectItem key={pace.value} value={pace.value}>
                  {pace.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Generate Button */}
        <Button 
          onClick={handleGenerateRoadmap}
          disabled={isGenerating}
          className="w-full h-12 text-lg"
        >
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              Generating Your Roadmap...
            </>
          ) : (
            <>
              <Sparkles className="mr-2 h-5 w-5" />
              Generate My Learning Roadmap
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};
