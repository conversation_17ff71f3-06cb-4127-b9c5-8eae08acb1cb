# JapanCoach Samurai Redesign - Product Requirements Document

## 1. Product Overview
Transform JapanCoach from its current playful, emoji-heavy design to a sophisticated, Samurai-inspired Japanese language learning platform that embodies the discipline, honor, and aesthetic principles of traditional Japanese warrior culture.

The redesign addresses the need for a more mature, professional appearance that appeals to serious learners while maintaining the core AI-powered learning functionality. The new design will reflect the values of dedication, precision, and respect inherent in both Samurai culture and language mastery.

## 2. Core Features

### 2.1 User Roles
| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| De<PERSON>ult Learner | Direct access | Can access all learning modules, track progress, interact with AI agents |
| Advanced Practitioner | Progress-based unlock | Can access advanced techniques, specialized training modules, mentor features |

### 2.2 Feature Module
Our Samurai-themed JapanCoach redesign consists of the following main pages:
1. **Dojo Entrance (Home)**: Traditional welcome gate, philosophy introduction, path selection
2. **Training Grounds (Dashboard)**: Progress tracking, daily discipline metrics, AI sensei selection
3. **Sensei Council (AI Agents)**: Traditional master-student interaction interface
4. **Progress Scroll (Analytics)**: Achievement tracking with traditional Japanese aesthetics
5. **Settings Shrine**: User preferences and customization options

### 2.3 Page Details
| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| Dojo Entrance | Hero Gate | Display traditional torii gate entrance with calligraphy welcome message, philosophy quote rotation |
| Dojo Entrance | Path Selection | Present learning paths as traditional scrolls with brush stroke navigation |
| Dojo Entrance | Philosophy Banner | Showcase Samurai principles applied to language learning with elegant typography |
| Training Grounds | Daily Discipline Tracker | Replace streak counter with traditional calendar system, show dedication metrics |
| Training Grounds | Progress Visualization | Use traditional Japanese charts and symbols to display learning statistics |
| Training Grounds | Sensei Selection | Present AI agents as traditional masters with formal portraits and specializations |
| Sensei Council | Master Profiles | Display each AI agent with traditional Japanese naming, formal descriptions, and specialization areas |
| Sensei Council | Interaction Interface | Create formal, respectful communication interface with traditional greetings and responses |
| Progress Scroll | Achievement System | Replace modern progress bars with traditional scroll-based achievement tracking |
| Progress Scroll | Skill Mastery | Visualize language skills using traditional Japanese ranking systems (dan/kyu) |
| Settings Shrine | Customization Options | Provide theme variations, difficulty settings, and learning preferences in shrine-like interface |

## 3. Core Process

**Primary Learning Flow:**
Users enter through the Dojo Entrance where they are welcomed with traditional Japanese aesthetics and philosophy. They select their learning path through scroll-based navigation, then proceed to the Training Grounds to track their daily discipline and select their preferred Sensei. The learning interaction occurs through the Sensei Council with formal, respectful communication patterns. Progress is tracked through the Progress Scroll using traditional Japanese achievement systems.

```mermaid
graph TD
    A[Dojo Entrance] --> B[Path Selection]
    B --> C[Training Grounds]
    C --> D[Sensei Council]
    D --> E[Learning Session]
    E --> F[Progress Scroll]
    F --> C
    C --> G[Settings Shrine]
```

## 4. User Interface Design

### 4.1 Design Style
- **Primary Colors**: Deep charcoal (#2C2C2C), Traditional red (#CC2936), Gold accent (#D4AF37)
- **Secondary Colors**: Warm white (#F5F5F5), Subtle gray (#6B6B6B), Dark navy (#1B1B2F)
- **Typography**: Serif fonts for headings (Noto Serif JP), Clean sans-serif for body text (Noto Sans JP)
- **Button Style**: Rectangular with subtle borders, inspired by traditional Japanese architecture
- **Layout Style**: Asymmetrical balance, generous white space, vertical text elements where appropriate
- **Icon Style**: Minimalist line art inspired by traditional Japanese symbols, no emojis
- **Textures**: Subtle paper textures, brush stroke elements, traditional pattern overlays

### 4.2 Page Design Overview
| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| Dojo Entrance | Hero Gate | Large torii gate silhouette with gradient background (#2C2C2C to #1B1B2F), traditional calligraphy welcome text in gold |
| Dojo Entrance | Path Selection | Vertical scroll elements with brush stroke borders, traditional Japanese paper texture background |
| Training Grounds | Daily Discipline | Minimalist cards with subtle shadows, traditional calendar grid, progress indicators using traditional symbols |
| Training Grounds | Sensei Selection | Formal portrait cards with traditional frames, elegant typography, respectful spacing |
| Sensei Council | Master Profiles | Large portrait areas with traditional Japanese name formatting, formal description text blocks |
| Progress Scroll | Achievement Display | Horizontal scroll interface with traditional Japanese ranking symbols, elegant progress visualization |
| Settings Shrine | Options Interface | Traditional shrine-inspired layout with wooden texture elements, formal toggle switches |

### 4.3 Responsiveness
Desktop-first approach with mobile adaptation maintaining the serious, traditional aesthetic. Touch interactions optimized for tablet use with gesture-based navigation inspired by traditional Japanese art forms. All traditional elements scale appropriately while maintaining their cultural authenticity and visual impact.

## 5. Technical Implementation Notes

### 5.1 Color Palette Implementation
- Replace current bright gradients (pink, purple, blue) with muted, traditional colors
- Implement custom CSS variables for consistent color theming
- Use subtle gradients only where they enhance the traditional aesthetic

### 5.2 Typography Changes
- Replace current system fonts with Google Fonts: Noto Serif JP and Noto Sans JP
- Implement proper Japanese text rendering and vertical text support where appropriate
- Ensure proper font weights for hierarchy (light, regular, medium, bold)

### 5.3 Component Redesign Priority
1. Navigation header - Transform to traditional horizontal layout
2. Hero section - Replace with torii gate and calligraphy
3. AI agent cards - Redesign as formal master profiles
4. Progress indicators - Replace with traditional Japanese symbols
5. Buttons and interactions - Implement rectangular, architectural-inspired design

### 5.4 Animation Guidelines
- Replace bouncy, playful animations with subtle, respectful transitions
- Implement brush stroke reveal animations for text elements
- Use fade and slide transitions that respect the serious tone
- Maintain smooth performance while adding traditional texture overlays