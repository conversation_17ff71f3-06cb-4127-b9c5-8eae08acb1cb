package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Grammar represents a Japanese grammar point
type Grammar struct {
	ID       uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	LessonID *uuid.UUID `json:"lesson_id" gorm:"type:uuid"` // Optional lesson association
	Lesson   *Lesson    `json:"lesson,omitempty" gorm:"foreignKey:LessonID"`

	// Grammar point details
	Title       string `json:"title" gorm:"not null"` // Grammar point name
	TitleJP     string `json:"title_jp"` // Japanese title
	Pattern     string `json:"pattern" gorm:"not null"` // Grammar pattern (e.g., "Verb + ている")
	Explanation string `json:"explanation" gorm:"type:text;not null"` // Detailed explanation
	Usage       string `json:"usage" gorm:"type:text"` // When and how to use
	
	// Classification
	Level    string `json:"level" gorm:"not null"` // beginner, intermediate, advanced
	JLPT     string `json:"jlpt"` // N5, N4, N3, N2, N1
	Category string `json:"category"` // tense, particle, honorific, etc.
	
	// Examples
	Examples string `json:"examples" gorm:"type:text"` // JSON array of example objects
	
	// Related grammar points
	RelatedGrammar string `json:"related_grammar"` // JSON array of related grammar IDs
	Prerequisites  string `json:"prerequisites"` // JSON array of prerequisite grammar IDs
	
	// Difficulty and frequency
	DifficultyRating int `json:"difficulty_rating" gorm:"default:1"` // 1-5
	UsageFrequency   int `json:"usage_frequency" gorm:"default:0"` // How commonly used
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Progress []GrammarProgress `json:"progress,omitempty"`
}

// GrammarProgress tracks user progress on grammar points
type GrammarProgress struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null"`
	GrammarID uuid.UUID `json:"grammar_id" gorm:"type:uuid;not null"`
	User      User      `json:"user" gorm:"foreignKey:UserID"`
	Grammar   Grammar   `json:"grammar" gorm:"foreignKey:GrammarID"`

	// Progress tracking
	MasteryLevel       int       `json:"mastery_level" gorm:"default:0"` // 0-100
	TimesStudied       int       `json:"times_studied" gorm:"default:0"`
	TimesCorrect       int       `json:"times_correct" gorm:"default:0"`
	TimesIncorrect     int       `json:"times_incorrect" gorm:"default:0"`
	LastStudiedAt      *time.Time `json:"last_studied_at"`
	NextReviewAt       *time.Time `json:"next_review_at"`
	
	// Understanding levels
	UnderstandsPattern bool `json:"understands_pattern" gorm:"default:false"`
	CanUseInSentence   bool `json:"can_use_in_sentence" gorm:"default:false"`
	CanExplainUsage    bool `json:"can_explain_usage" gorm:"default:false"`
	
	// Spaced repetition
	Interval           int     `json:"interval" gorm:"default:1"` // days until next review
	EaseFactor         float64 `json:"ease_factor" gorm:"default:2.5"`
	ConsecutiveCorrect int     `json:"consecutive_correct" gorm:"default:0"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeCreate sets the ID before creating
func (g *Grammar) BeforeCreate(tx *gorm.DB) error {
	if g.ID == uuid.Nil {
		g.ID = uuid.New()
	}
	return nil
}

func (gp *GrammarProgress) BeforeCreate(tx *gorm.DB) error {
	if gp.ID == uuid.Nil {
		gp.ID = uuid.New()
	}
	return nil
}
