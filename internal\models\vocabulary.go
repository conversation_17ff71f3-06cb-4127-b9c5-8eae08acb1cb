package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Vocabulary represents a Japanese vocabulary word
type Vocabulary struct {
	ID       uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	LessonID *uuid.UUID `json:"lesson_id" gorm:"type:uuid"` // Optional lesson association
	Lesson   *Lesson    `json:"lesson,omitempty" gorm:"foreignKey:LessonID"`

	// Word details
	Word        string `json:"word" gorm:"not null"` // Japanese word
	Reading     string `json:"reading"` // Hiragana/Katakana reading
	Romaji      string `json:"romaji"` // Romanized version
	Meaning     string `json:"meaning" gorm:"not null"` // English meaning
	PartOfSpeech string `json:"part_of_speech"` // noun, verb, adjective, etc.
	
	// Additional information
	Level       string `json:"level" gorm:"not null"` // beginner, intermediate, advanced
	JLPT        string `json:"jlpt"` // N5, N4, N3, N2, N1
	Frequency   int    `json:"frequency" gorm:"default:0"` // Usage frequency ranking
	
	// Example usage
	ExampleSentence   string `json:"example_sentence"`
	ExampleTranslation string `json:"example_translation"`
	ExampleRomaji     string `json:"example_romaji"`
	
	// Audio
	AudioURL string `json:"audio_url"`
	
	// Tags and categories
	Tags     string `json:"tags"` // JSON array of tags
	Category string `json:"category"` // daily_life, business, travel, etc.
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Progress []VocabularyProgress `json:"progress,omitempty"`
}

// VocabularyProgress tracks user progress on vocabulary words
type VocabularyProgress struct {
	ID           uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID       uuid.UUID  `json:"user_id" gorm:"type:uuid;not null"`
	VocabularyID uuid.UUID  `json:"vocabulary_id" gorm:"type:uuid;not null"`
	User         User       `json:"user" gorm:"foreignKey:UserID"`
	Vocabulary   Vocabulary `json:"vocabulary" gorm:"foreignKey:VocabularyID"`

	// Progress tracking
	MasteryLevel    int       `json:"mastery_level" gorm:"default:0"` // 0-100
	TimesReviewed   int       `json:"times_reviewed" gorm:"default:0"`
	TimesCorrect    int       `json:"times_correct" gorm:"default:0"`
	TimesIncorrect  int       `json:"times_incorrect" gorm:"default:0"`
	LastReviewedAt  *time.Time `json:"last_reviewed_at"`
	NextReviewAt    *time.Time `json:"next_review_at"`
	
	// Spaced repetition
	Interval        int       `json:"interval" gorm:"default:1"` // days until next review
	EaseFactor      float64   `json:"ease_factor" gorm:"default:2.5"`
	ConsecutiveCorrect int    `json:"consecutive_correct" gorm:"default:0"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeCreate sets the ID before creating
func (v *Vocabulary) BeforeCreate(tx *gorm.DB) error {
	if v.ID == uuid.Nil {
		v.ID = uuid.New()
	}
	return nil
}

func (vp *VocabularyProgress) BeforeCreate(tx *gorm.DB) error {
	if vp.ID == uuid.Nil {
		vp.ID = uuid.New()
	}
	return nil
}
