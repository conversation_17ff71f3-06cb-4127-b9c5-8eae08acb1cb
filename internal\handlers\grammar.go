package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"japancoach-backend/internal/middleware"
	"japancoach-backend/internal/models"
)

type GrammarHandler struct {
	db *gorm.DB
}

func NewGrammarHandler(db *gorm.DB) *GrammarHandler {
	return &GrammarHandler{db: db}
}

// GetGrammarPoints returns grammar points based on user's level and filters
func (h *GrammarHandler) GetGrammarPoints(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user to determine their level
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Parse query parameters
	level := c.DefaultQuery("level", user.JapaneseLevel)
	category := c.Query("category")
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")
	needsReview := c.Query("needs_review") == "true"

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)

	// Build query
	query := h.db.Model(&models.Grammar{}).
		Where("level = ?", level)

	if category != "" {
		query = query.Where("category = ?", category)
	}

	// If needs_review is true, get grammar points with low mastery
	if needsReview {
		subQuery := h.db.Model(&models.GrammarProgress{}).
			Select("grammar_id").
			Where("user_id = ? AND mastery_level < ?", user.ID, 80)

		query = query.Where("id IN (?)", subQuery)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get grammar points with user progress
	var grammarPoints []models.Grammar
	if err := query.
		Preload("Progress", "user_id = ?", user.ID).
		Order("difficulty ASC, title ASC").
		Limit(limit).
		Offset(offset).
		Find(&grammarPoints).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch grammar points"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"grammar_points": grammarPoints,
		"total":          total,
		"limit":          limit,
		"offset":         offset,
	})
}

// GetGrammarPoint returns a specific grammar point by ID
func (h *GrammarHandler) GetGrammarPoint(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	grammarID := c.Param("id")
	grammarUUID, err := uuid.Parse(grammarID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid grammar ID"})
		return
	}

	var grammar models.Grammar
	if err := h.db.
		Preload("Progress", "user_id = ?", user.ID).
		Where("id = ?", grammarUUID).
		First(&grammar).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Grammar point not found"})
		return
	}

	c.JSON(http.StatusOK, grammar)
}

// GetGrammarCategories returns available grammar categories
func (h *GrammarHandler) GetGrammarCategories(c *gin.Context) {
	var categories []string
	if err := h.db.Model(&models.Grammar{}).
		Distinct("category").
		Where("category IS NOT NULL AND category != ''").
		Pluck("category", &categories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"categories": categories,
	})
}

// SearchGrammar searches grammar points by title, pattern, or explanation
func (h *GrammarHandler) SearchGrammar(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	searchTerm := c.Query("q")
	if searchTerm == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search term is required"})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, _ := strconv.Atoi(limitStr)

	var grammarPoints []models.Grammar
	if err := h.db.
		Preload("Progress", "user_id = ?", user.ID).
		Where("title ILIKE ? OR pattern ILIKE ? OR explanation ILIKE ?",
			"%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%").
		Order("difficulty ASC").
		Limit(limit).
		Find(&grammarPoints).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search grammar"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"grammar_points": grammarPoints,
		"count":          len(grammarPoints),
		"search_term":    searchTerm,
	})
}

// GetGrammarStats returns grammar learning statistics
func (h *GrammarHandler) GetGrammarStats(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Get grammar statistics
	var stats struct {
		Total    int64 `json:"total"`
		Mastered int64 `json:"mastered"`
		Learning int64 `json:"learning"`
		New      int64 `json:"new"`
	}

	// Total grammar points in progress
	h.db.Model(&models.GrammarProgress{}).
		Where("user_id = ?", user.ID).
		Count(&stats.Total)

	// Mastered (>= 80% mastery)
	h.db.Model(&models.GrammarProgress{}).
		Where("user_id = ? AND mastery_level >= ?", user.ID, 80).
		Count(&stats.Mastered)

	// Learning (0-79% mastery)
	h.db.Model(&models.GrammarProgress{}).
		Where("user_id = ? AND mastery_level > ? AND mastery_level < ?", user.ID, 0, 80).
		Count(&stats.Learning)

	// New grammar points (not yet started)
	var totalAvailable int64
	h.db.Model(&models.Grammar{}).Count(&totalAvailable)
	stats.New = totalAvailable - stats.Total

	// Get stats by level
	var levelStats []struct {
		Level    string `json:"level"`
		Total    int64  `json:"total"`
		Mastered int64  `json:"mastered"`
	}

	h.db.Raw(`
		SELECT 
			g.level,
			COUNT(*) as total,
			COUNT(CASE WHEN gp.mastery_level >= 80 THEN 1 END) as mastered
		FROM grammar g
		LEFT JOIN grammar_progress gp ON g.id = gp.grammar_id AND gp.user_id = ?
		WHERE g.level IS NOT NULL
		GROUP BY g.level
		ORDER BY 
			CASE g.level 
				WHEN 'beginner' THEN 1 
				WHEN 'intermediate' THEN 2 
				WHEN 'advanced' THEN 3 
				ELSE 4 
			END
	`, user.ID).Scan(&levelStats)

	// Get stats by category
	var categoryStats []struct {
		Category string `json:"category"`
		Total    int64  `json:"total"`
		Mastered int64  `json:"mastered"`
	}

	h.db.Raw(`
		SELECT 
			g.category,
			COUNT(*) as total,
			COUNT(CASE WHEN gp.mastery_level >= 80 THEN 1 END) as mastered
		FROM grammar g
		LEFT JOIN grammar_progress gp ON g.id = gp.grammar_id AND gp.user_id = ?
		WHERE g.category IS NOT NULL
		GROUP BY g.category
		ORDER BY g.category
	`, user.ID).Scan(&categoryStats)

	c.JSON(http.StatusOK, gin.H{
		"overall":     stats,
		"by_level":    levelStats,
		"by_category": categoryStats,
	})
}

// GetGrammarExercises returns practice exercises for a grammar point
func (h *GrammarHandler) GetGrammarExercises(c *gin.Context) {
	_, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	grammarID := c.Param("id")
	grammarUUID, err := uuid.Parse(grammarID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid grammar ID"})
		return
	}

	// Verify grammar point exists
	var grammar models.Grammar
	if err := h.db.Where("id = ?", grammarUUID).First(&grammar).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Grammar point not found"})
		return
	}

	// Generate practice exercises (in a real app, these would be stored in the database)
	exercises := h.generateGrammarExercises(grammar)

	c.JSON(http.StatusOK, gin.H{
		"grammar_point": grammar,
		"exercises":     exercises,
	})
}

// Helper function to generate grammar exercises
func (h *GrammarHandler) generateGrammarExercises(grammar models.Grammar) []gin.H {
	// This is a simplified example - in a real app, exercises would be stored in the database
	exercises := []gin.H{
		{
			"type":        "fill_blank",
			"question":    "彼は毎日学校___行きます。",
			"options":     []string{"に", "で", "を", "が"},
			"correct":     "に",
			"explanation": "「行く」の動詞には「に」を使います。",
		},
		{
			"type":        "translation",
			"question":    "I go to school every day.",
			"answer":      "私は毎日学校に行きます。",
			"explanation": "「毎日」は「every day」、「学校に行く」は「go to school」です。",
		},
		{
			"type":        "pattern_recognition",
			"question":    "次の文で正しい文法パターンを選んでください：",
			"sentence":    "友達と映画を見に行きました。",
			"pattern":     grammar.Pattern,
			"explanation": grammar.Explanation,
		},
	}

	return exercises
}
