import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RoadmapGenerator } from '@/components/RoadmapGenerator';
import { RoadmapDisplay } from '@/components/RoadmapDisplay';
import { 
  Plus, 
  BookOpen, 
  Target, 
  Calendar,
  ArrowLeft,
  Loader2
} from 'lucide-react';

interface Roadmap {
  id: string;
  title: string;
  description: string;
  target_level: string;
  estimated_duration: number;
  completion_rate: number;
  status: string;
  created_at: string;
  milestones: any[];
}

export const Roadmap: React.FC = () => {
  const [roadmaps, setRoadmaps] = useState<Roadmap[]>([]);
  const [selectedRoadmap, setSelectedRoadmap] = useState<Roadmap | null>(null);
  const [showGenerator, setShowGenerator] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneratingLesson, setIsGeneratingLesson] = useState<string | null>(null);

  useEffect(() => {
    fetchRoadmaps();
  }, []);

  const fetchRoadmaps = async () => {
    try {
      const response = await fetch('/api/v1/roadmaps', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRoadmaps(data.roadmaps || []);
      }
    } catch (error) {
      console.error('Error fetching roadmaps:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoadmapGenerated = (newRoadmap: Roadmap) => {
    setRoadmaps(prev => [newRoadmap, ...prev]);
    setSelectedRoadmap(newRoadmap);
    setShowGenerator(false);
  };

  const handleGenerateLesson = async (lessonPlanId: string) => {
    setIsGeneratingLesson(lessonPlanId);
    
    try {
      const response = await fetch(`/api/v1/lesson-plans/${lessonPlanId}/generate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        // Refresh the current roadmap to show updated lesson status
        if (selectedRoadmap) {
          const roadmapResponse = await fetch(`/api/v1/roadmaps/${selectedRoadmap.id}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });
          
          if (roadmapResponse.ok) {
            const updatedRoadmap = await roadmapResponse.json();
            setSelectedRoadmap(updatedRoadmap);
          }
        }
      } else {
        alert('Failed to generate lesson. Please try again.');
      }
    } catch (error) {
      console.error('Error generating lesson:', error);
      alert('Failed to generate lesson. Please try again.');
    } finally {
      setIsGeneratingLesson(null);
    }
  };

  const handleStartLesson = async (lessonPlanId: string) => {
    try {
      const response = await fetch(`/api/v1/lesson-plans/${lessonPlanId}/lesson`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const lessonData = await response.json();
        // TODO: Navigate to lesson view or open lesson modal
        console.log('Starting lesson:', lessonData);
        alert('Lesson content loaded! (Navigation to lesson view will be implemented)');
      } else {
        alert('Failed to load lesson content.');
      }
    } catch (error) {
      console.error('Error starting lesson:', error);
      alert('Failed to load lesson content.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-900 via-slate-800 to-gray-900 flex items-center justify-center">
        <div className="text-center text-white">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading your roadmaps...</p>
        </div>
      </div>
    );
  }

  if (showGenerator) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-900 via-slate-800 to-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          <Button
            onClick={() => setShowGenerator(false)}
            variant="ghost"
            className="text-white hover:text-gray-300 mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Roadmaps
          </Button>
          <RoadmapGenerator onRoadmapGenerated={handleRoadmapGenerated} />
        </div>
      </div>
    );
  }

  if (selectedRoadmap) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-900 via-slate-800 to-gray-900 p-6">
        <div className="max-w-6xl mx-auto">
          <Button
            onClick={() => setSelectedRoadmap(null)}
            variant="ghost"
            className="text-white hover:text-gray-300 mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Roadmaps
          </Button>
          <RoadmapDisplay 
            roadmap={selectedRoadmap}
            onGenerateLesson={handleGenerateLesson}
            onStartLesson={handleStartLesson}
            isGeneratingLesson={isGeneratingLesson}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-slate-800 to-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            🗾 Learning Roadmaps
          </h1>
          <p className="text-gray-300 text-lg">
            AI-powered personalized learning paths for mastering Japanese
          </p>
        </div>

        {/* Create New Roadmap Button */}
        <div className="mb-8 text-center">
          <Button
            onClick={() => setShowGenerator(true)}
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3"
          >
            <Plus className="h-5 w-5 mr-2" />
            Create New Roadmap
          </Button>
        </div>

        {/* Roadmaps List */}
        {roadmaps.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                No roadmaps yet
              </h3>
              <p className="text-gray-500 mb-6">
                Create your first AI-generated learning roadmap to get started on your Japanese journey!
              </p>
              <Button
                onClick={() => setShowGenerator(true)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Roadmap
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {roadmaps.map((roadmap) => (
              <Card 
                key={roadmap.id} 
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => setSelectedRoadmap(roadmap)}
              >
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg line-clamp-2">
                      {roadmap.title}
                    </CardTitle>
                    <Badge className={getStatusColor(roadmap.status)}>
                      {roadmap.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {roadmap.description}
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="flex items-center gap-1">
                        <Target className="h-4 w-4" />
                        {roadmap.target_level}
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {roadmap.estimated_duration} days
                      </span>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{roadmap.completion_rate.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${roadmap.completion_rate}%` }}
                        />
                      </div>
                    </div>

                    <div className="text-xs text-muted-foreground">
                      Created {new Date(roadmap.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
