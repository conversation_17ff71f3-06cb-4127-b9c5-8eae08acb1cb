import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Sword, Mountain, BookOpen, Users, Target, Award } from 'lucide-react';

export default function Home() {
  const navigate = useNavigate();
  const [scrollY, setScrollY] = useState(0);
  const [currentPhilosophy, setCurrentPhilosophy] = useState(0);
  
  const samuraiPhilosophies = [
    { jp: '武士道', en: 'Bushido - The Way of the Warrior' },
    { jp: '礼儀', en: 'Reigi - Respect and Courtesy' },
    { jp: '忍耐', en: 'Nintai - Patience and Endurance' },
    { jp: '精神', en: 'Seishin - Spirit and Mind' },
    { jp: '修行', en: 'Shugyō - Disciplined Training' }
  ];
  
  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPhilosophy((prev) => (prev + 1) % samuraiPhilosophies.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-900 via-gray-900 to-black overflow-hidden">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/80 backdrop-blur-sm border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Sword className="w-8 h-8 text-red-600" />
              <span className="text-2xl font-serif-jp font-bold text-white">道場コーチ</span>
              <span className="text-lg font-sans-jp text-gray-300">Dōjō Coach</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#training" className="text-gray-300 hover:text-white transition-colors font-sans-jp">Training</a>
              <a href="#sensei" className="text-gray-300 hover:text-white transition-colors font-sans-jp">Sensei</a>
              <button 
                onClick={() => navigate('/dashboard')}
                className="samurai-button"
              >
                Enter Dojo
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Dojo Entrance Hero */}
      <section className="relative min-h-screen flex items-center justify-center">
        {/* Traditional Background Elements */}
        <div className="absolute inset-0 overflow-hidden -z-10">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 border border-gray-700 rotate-45 opacity-20 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-24 h-24 border border-gray-700 rotate-12 opacity-20 animate-bounce"></div>
        </div>

        {/* Torii Gate */}
        <div className="absolute top-20 left-1/2 transform -translate-x-1/2">
          <div className="torii-gate opacity-60"></div>
        </div>

        {/* Main Hero Content */}
        <div className="relative z-10 text-center max-w-5xl mx-auto px-6">
          <div className="paper-texture rounded-none p-16 border-2 border-gray-800 shadow-2xl">
            {/* Philosophy Banner */}
            <div className="mb-8 animate-fade-in-up">
              <div className="text-4xl font-serif-jp text-gray-800 mb-2">
                {samuraiPhilosophies[currentPhilosophy].jp}
              </div>
              <div className="text-lg font-sans-jp text-gray-600">
                {samuraiPhilosophies[currentPhilosophy].en}
              </div>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-serif-jp font-bold text-gray-900 mb-6 leading-tight">
              日本語の道
              <br />
              <span className="text-3xl md:text-4xl font-sans-jp text-gray-700">The Path of Japanese Mastery</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-700 mb-8 leading-relaxed font-sans-jp">
              Embark on a disciplined journey of language mastery guided by AI Sensei.
              <br />Honor the tradition. Embrace the challenge. Achieve excellence.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <button 
                onClick={() => navigate('/dashboard')}
                className="samurai-button text-lg"
              >
                <Sword className="w-5 h-5 inline mr-2" />
                Begin Training
              </button>
              <button className="bg-transparent border-2 border-gray-800 text-gray-800 px-8 py-4 font-sans-jp font-medium hover:bg-gray-800 hover:text-white transition-all duration-300">
                <BookOpen className="w-5 h-5 inline mr-2" />
                Study Philosophy
              </button>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-gentle-float">
          <div className="w-1 h-16 bg-gray-600 opacity-60"></div>
        </div>
      </section>

      {/* Training Disciplines Section */}
      <section id="training" className="py-20 relative bg-gray-100">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-serif-jp font-bold text-gray-900 mb-6 brush-stroke">修行の道</h2>
            <p className="text-xl font-sans-jp text-gray-700 max-w-3xl mx-auto">
              Master the fundamental disciplines of Japanese language through structured training and unwavering dedication.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: <Users className="w-12 h-12" />,
                title: 'Conversation Mastery',
                description: 'Engage in respectful dialogue with AI Sensei, learning proper etiquette and natural expression patterns.'
              },
              {
                icon: <BookOpen className="w-12 h-12" />,
                title: 'Grammar Foundation',
                description: 'Build unshakeable grammatical understanding through methodical study and disciplined practice.'
              },
              {
                icon: <Target className="w-12 h-12" />,
                title: 'Precision Writing',
                description: 'Develop elegant writing skills with careful attention to form, meaning, and cultural context.'
              }
            ].map((discipline, index) => (
              <div key={index} className="group">
                <div className="samurai-card p-8 hover:shadow-lg transition-all duration-300">
                  <div className="text-gray-800 mb-6 group-hover:text-red-600 transition-colors duration-300">{discipline.icon}</div>
                  <h3 className="text-2xl font-serif-jp font-bold text-gray-900 mb-4">{discipline.title}</h3>
                  <p className="text-gray-700 leading-relaxed font-sans-jp">{discipline.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Sensei Council Section */}
      <section id="sensei" className="py-20 relative bg-gradient-to-b from-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-serif-jp font-bold text-white mb-6 brush-stroke">先生評議会</h2>
            <p className="text-xl font-sans-jp text-gray-300 max-w-3xl mx-auto">
              Honor the wisdom of your AI Sensei. Each master brings decades of virtual experience and specialized knowledge to guide your journey.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: '田中先生',
                romanji: 'Tanaka-sensei',
                role: 'Master of Conversation',
                philosophy: 'Through respectful dialogue, we discover truth',
                specialization: 'Formal & Casual Speech Patterns',
                rank: '九段'
              },
              {
                name: '山田先生',
                romanji: 'Yamada-sensei',
                role: 'Guardian of Grammar',
                philosophy: 'Precision in structure reflects clarity of mind',
                specialization: 'Classical & Modern Grammar',
                rank: '八段'
              },
              {
                name: '佐藤先生',
                romanji: 'Satō-sensei',
                role: 'Keeper of Written Arts',
                philosophy: 'Each character carries the weight of history',
                specialization: 'Calligraphy & Composition',
                rank: '七段'
              }
            ].map((sensei, index) => (
              <div key={index} className="group">
                <div className="bg-gray-800 border-2 border-gray-700 p-8 hover:border-red-600 transition-all duration-300">
                  <div className="text-center">
                    <div className="w-24 h-24 mx-auto mb-6 bg-gray-700 border-2 border-gray-600 flex items-center justify-center group-hover:border-red-600 transition-colors duration-300">
                      <span className="text-2xl font-serif-jp text-white">{sensei.rank}</span>
                    </div>
                    <h3 className="text-2xl font-serif-jp font-bold text-white mb-1">{sensei.name}</h3>
                    <p className="text-lg font-sans-jp text-gray-400 mb-2">{sensei.romanji}</p>
                    <p className="text-red-400 font-sans-jp font-medium mb-4">{sensei.role}</p>
                    <p className="text-gray-300 italic font-sans-jp text-sm mb-4">"{sensei.philosophy}"</p>
                    <p className="text-gray-400 text-sm font-sans-jp mb-6">{sensei.specialization}</p>
                    <div className="mt-6">
                      <button className="samurai-button w-full">
                        Request Guidance
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 relative bg-gray-100">
        <div className="max-w-4xl mx-auto text-center px-6">
          <div className="paper-texture p-16 border-2 border-gray-800">
            <h2 className="text-4xl font-serif-jp font-bold text-gray-900 mb-6 brush-stroke">道への招待</h2>
            <p className="text-xl font-sans-jp text-gray-700 mb-8">
              The path of mastery begins with a single step.
              <br />Honor your commitment. Embrace discipline. Achieve excellence.
            </p>
            <button 
              onClick={() => navigate('/dashboard')}
              className="samurai-button text-xl px-12 py-4"
            >
              <Award className="w-6 h-6 inline mr-2" />
              Enter the Training Grounds
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-black border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <Sword className="w-6 h-6 text-red-600" />
            <span className="text-xl font-serif-jp font-bold text-white">道場コーチ</span>
          </div>
          <p className="text-gray-400 font-sans-jp">Forged with Honor • Guided by Tradition • Powered by Innovation</p>
        </div>
      </footer>
    </div>
  );
}