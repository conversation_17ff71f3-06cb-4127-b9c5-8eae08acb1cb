package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Lesson represents a Japanese lesson
type Lesson struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Title       string    `json:"title" gorm:"not null"`
	TitleJP     string    `json:"title_jp"` // Japanese title
	Description string    `json:"description"`
	Level       string    `json:"level" gorm:"not null"` // beginner, intermediate, advanced
	Category    string    `json:"category" gorm:"not null"` // conversation, grammar, vocabulary, writing
	
	// Sensei assignment
	AssignedSensei string `json:"assigned_sensei" gorm:"default:'tanaka'"` // tanaka, yamada, sato
	
	// Lesson metadata
	EstimatedDuration int    `json:"estimated_duration"` // in minutes
	DifficultyRating  int    `json:"difficulty_rating" gorm:"default:1"` // 1-5
	Prerequisites     string `json:"prerequisites"` // JSON array of lesson IDs
	
	// Ordering and organization
	Order      int  `json:"order" gorm:"default:0"`
	IsPublished bool `json:"is_published" gorm:"default:true"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Content     []LessonContent `json:"content,omitempty"`
	Vocabulary  []Vocabulary    `json:"vocabulary,omitempty"`
	Grammar     []Grammar       `json:"grammar,omitempty"`
}

// LessonContent represents the content blocks of a lesson
type LessonContent struct {
	ID       uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	LessonID uuid.UUID `json:"lesson_id" gorm:"type:uuid;not null"`
	Lesson   Lesson    `json:"lesson" gorm:"foreignKey:LessonID"`

	// Content details
	Type        string `json:"type" gorm:"not null"` // text, dialogue, exercise, audio, video
	Title       string `json:"title"`
	Content     string `json:"content" gorm:"type:text"` // Main content (text, HTML, JSON)
	ContentJP   string `json:"content_jp" gorm:"type:text"` // Japanese version
	AudioURL    string `json:"audio_url"`
	ImageURL    string `json:"image_url"`
	
	// Exercise specific fields
	ExerciseType string `json:"exercise_type"` // multiple_choice, fill_blank, translation, pronunciation
	Questions    string `json:"questions" gorm:"type:text"` // JSON array of questions
	Answers      string `json:"answers" gorm:"type:text"` // JSON array of correct answers
	
	// Ordering
	Order int `json:"order" gorm:"default:0"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeCreate sets the ID before creating
func (l *Lesson) BeforeCreate(tx *gorm.DB) error {
	if l.ID == uuid.Nil {
		l.ID = uuid.New()
	}
	return nil
}

func (lc *LessonContent) BeforeCreate(tx *gorm.DB) error {
	if lc.ID == uuid.Nil {
		lc.ID = uuid.New()
	}
	return nil
}
