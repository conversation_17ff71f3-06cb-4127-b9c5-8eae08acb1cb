import { useEffect, useState } from 'react';

interface FloatingChar {
  id: number;
  char: string;
  x: number;
  y: number;
  size: string;
  direction: 'right' | 'left' | 'down';
  duration: number;
  delay: number;
}

const JapaneseBackground = () => {
  const [chars, setChars] = useState<FloatingChar[]>([]);

  // Traditional Japanese characters for animation
  const japaneseChars = [
    '武', '士', '道', '心', '剣', '刀', '侍', '忍', '禅', '和',
    '美', '雅', '静', '清', '純', '誠', '義', '勇', '礼', '智',
    '仁', '信', '忠', '孝', '恭', '敬', '謙', '慎', '節', '制',
    '学', '習', '練', '修', '行', '鍛', '錬', '精', '神', '魂',
    '桜', '花', '月', '雪', '風', '雲', '山', '川', '海', '空'
  ];

  const sizes = ['size-sm', 'size-md', 'size-lg', 'size-xl'];
  const directions: ('right' | 'left' | 'down')[] = ['right', 'left', 'down'];

  useEffect(() => {
    const generateChar = (): FloatingChar => {
      const direction = directions[Math.floor(Math.random() * directions.length)];
      let x, y;
      
      if (direction === 'right') {
        x = -100;
        y = Math.random() * window.innerHeight;
      } else if (direction === 'left') {
        x = window.innerWidth + 100;
        y = Math.random() * window.innerHeight;
      } else {
        x = Math.random() * window.innerWidth;
        y = -100;
      }

      return {
        id: Math.random(),
        char: japaneseChars[Math.floor(Math.random() * japaneseChars.length)],
        x,
        y,
        size: sizes[Math.floor(Math.random() * sizes.length)],
        direction,
        duration: 15 + Math.random() * 25, // 15-40 seconds
        delay: Math.random() * 5 // 0-5 seconds delay
      };
    };

    const addChar = () => {
      setChars(prev => {
        const newChars = [...prev, generateChar()];
        // Keep only the last 20 characters to prevent memory issues
        return newChars.slice(-20);
      });
    };

    // Add initial characters
    for (let i = 0; i < 8; i++) {
      setTimeout(() => addChar(), i * 1000);
    }

    // Continue adding characters periodically
    const interval = setInterval(addChar, 3000 + Math.random() * 4000); // Every 3-7 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="japanese-bg">
      {chars.map((char) => (
        <div
          key={char.id}
          className={`japanese-char ${char.size} traverse-${char.direction}`}
          style={{
            left: char.x,
            top: char.y,
            animationDuration: `${char.duration}s`,
            animationDelay: `${char.delay}s`
          }}
        >
          {char.char}
        </div>
      ))}
    </div>
  );
};

export default JapaneseBackground;