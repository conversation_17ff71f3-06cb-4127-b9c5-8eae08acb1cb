package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ClerkID   string    `json:"clerk_id" gorm:"uniqueIndex;not null"`
	Email     string    `json:"email" gorm:"uniqueIndex;not null"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Username  string    `json:"username" gorm:"uniqueIndex"`
	
	// Japanese learning specific fields
	JapaneseLevel    string    `json:"japanese_level" gorm:"default:'beginner'"` // beginner, intermediate, advanced
	PreferredSensei  string    `json:"preferred_sensei" gorm:"default:'tanaka'"`  // tanaka, yamada, sato
	StudyStreak      int       `json:"study_streak" gorm:"default:0"`
	TotalStudyTime   int       `json:"total_study_time" gorm:"default:0"` // in minutes
	LastStudyDate    *time.Time `json:"last_study_date"`
	
	// Gamification
	ExperiencePoints int       `json:"experience_points" gorm:"default:0"`
	CurrentRank      string    `json:"current_rank" gorm:"default:'白帯'"` // 白帯 (white belt)
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Progress     []UserProgress        `json:"progress,omitempty"`
	Achievements []UserAchievement     `json:"achievements,omitempty"`
	Conversations []ConversationSession `json:"conversations,omitempty"`
}

// UserProgress tracks overall user progress
type UserProgress struct {
	ID     uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;not null"`
	User   User      `json:"user" gorm:"foreignKey:UserID"`

	// Progress metrics
	LessonsCompleted    int `json:"lessons_completed" gorm:"default:0"`
	VocabularyMastered  int `json:"vocabulary_mastered" gorm:"default:0"`
	GrammarPointsLearned int `json:"grammar_points_learned" gorm:"default:0"`
	
	// Skill levels (0-100)
	ReadingLevel    int `json:"reading_level" gorm:"default:0"`
	WritingLevel    int `json:"writing_level" gorm:"default:0"`
	ListeningLevel  int `json:"listening_level" gorm:"default:0"`
	SpeakingLevel   int `json:"speaking_level" gorm:"default:0"`
	
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeCreate sets the ID before creating
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}

func (up *UserProgress) BeforeCreate(tx *gorm.DB) error {
	if up.ID == uuid.Nil {
		up.ID = uuid.New()
	}
	return nil
}
